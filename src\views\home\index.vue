<script setup lang="ts">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import StatisticCard from './modules/statistic-card.vue';
import TrendChart from './modules/trend-chart.vue';
import CategoryChart from './modules/category-chart.vue';
import PopularWallpapers from './modules/popular-wallpapers.vue';
import RecentActivity from './modules/recent-activity.vue';

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 统计卡片 -->
    <StatisticCard />
    
    <!-- 图表区域 -->
    <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
      <NGi span="24 s:24 m:14">
        <NCard :bordered="false" class="card-wrapper" title="上传/下载趋势">
          <TrendChart />
        </NCard>
      </NGi>
      <NGi span="24 s:24 m:10">
        <NCard :bordered="false" class="card-wrapper" title="分类分布">
          <CategoryChart />
        </NCard>
      </NGi>
    </NGrid>
    
    <!-- 热门壁纸和最近活动 -->
    <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive>
      <NGi span="24 s:24 m:14">
        <PopularWallpapers />
      </NGi>
      <NGi span="24 s:24 m:10">
        <RecentActivity />
      </NGi>
    </NGrid>
  </NSpace>
</template>

<style scoped></style>
