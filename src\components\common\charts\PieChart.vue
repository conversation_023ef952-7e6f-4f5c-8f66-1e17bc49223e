<script setup lang="ts">
import { computed } from 'vue';
import BaseChart from './BaseChart.vue';
import type { EChartsOption } from 'echarts';

interface DataItem {
  name: string;
  value: number;
  color?: string;
}

interface Props {
  data: DataItem[];
  title?: string;
  subtitle?: string;
  radius?: string | [string, string];
  center?: [string, string];
  showLegend?: boolean;
  legendPosition?: 'top' | 'bottom' | 'left' | 'right';
  showLabel?: boolean;
  labelPosition?: 'outside' | 'inside' | 'center';
  showPercentage?: boolean;
  roseType?: boolean | 'radius' | 'area';
  height?: string | number;
  width?: string | number;
  loading?: boolean;
  colors?: string[];
  innerRadius?: string;
  outerRadius?: string;
}

const props = withDefaults(defineProps<Props>(), {
  radius: '70%',
  center: () => ['50%', '50%'],
  showLegend: true,
  legendPosition: 'bottom',
  showLabel: true,
  labelPosition: 'outside',
  showPercentage: true,
  roseType: false,
  height: '400px',
  width: '100%',
  loading: false,
  colors: () => [
    '#2080f0', '#18a058', '#f0a020', '#d03050', '#722ed1',
    '#eb2f96', '#fa8c16', '#a0d911', '#13c2c2', '#1890ff'
  ],
  innerRadius: '0%',
  outerRadius: '70%'
});

const emit = defineEmits<{
  chartReady: [chart: any];
  chartClick: [params: any];
  chartHover: [params: any];
}>();

// 计算总值
const totalValue = computed(() => {
  return props.data.reduce((sum, item) => sum + item.value, 0);
});

// 处理数据，添加百分比
const processedData = computed(() => {
  return props.data.map((item, index) => ({
    ...item,
    percentage: ((item.value / totalValue.value) * 100).toFixed(1),
    itemStyle: {
      color: item.color || props.colors[index % props.colors.length]
    }
  }));
});

// 生成图表配置
const chartOption = computed<EChartsOption>(() => {
  const legendData = props.data.map(item => item.name);
  
  const option: EChartsOption = {
    color: props.colors,
    title: props.title ? {
      text: props.title,
      subtext: props.subtitle,
      left: 'center',
      top: '5%',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    } : undefined,
    tooltip: {
      trigger: 'item',
      formatter: (params: any) => {
        const percentage = ((params.value / totalValue.value) * 100).toFixed(1);
        return `${params.marker}${params.name}<br/>数值: ${params.value.toLocaleString()}<br/>占比: ${percentage}%`;
      }
    },
    legend: props.showLegend ? {
      data: legendData,
      [props.legendPosition]: props.legendPosition === 'top' || props.legendPosition === 'bottom' ? '5%' : '5%',
      orient: props.legendPosition === 'left' || props.legendPosition === 'right' ? 'vertical' : 'horizontal',
      itemGap: 10,
      textStyle: {
        fontSize: 12
      }
    } : undefined,
    series: [
      {
        type: 'pie',
        radius: Array.isArray(props.radius) ? props.radius : [props.innerRadius, props.outerRadius],
        center: props.center,
        data: processedData.value,
        roseType: props.roseType,
        label: props.showLabel ? {
          show: true,
          position: props.labelPosition,
          formatter: (params: any) => {
            if (props.labelPosition === 'center') {
              return `{name|${params.name}}\n{value|${params.value}}`;
            }
            if (props.showPercentage) {
              return `${params.name}\n${params.percent}%`;
            }
            return `${params.name}\n${params.value}`;
          },
          rich: props.labelPosition === 'center' ? {
            name: {
              fontSize: 14,
              fontWeight: 'bold',
              color: '#333'
            },
            value: {
              fontSize: 18,
              fontWeight: 'bold',
              color: '#666',
              padding: [5, 0, 0, 0]
            }
          } : undefined
        } : {
          show: false
        },
        labelLine: props.labelPosition === 'outside' ? {
          show: true,
          length: 15,
          length2: 10
        } : {
          show: false
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          },
          label: {
            show: true,
            fontSize: 14,
            fontWeight: 'bold'
          }
        },
        animationType: 'scale',
        animationEasing: 'elasticOut',
        animationDelay: (idx: number) => Math.random() * 200
      }
    ]
  };

  return option;
});

// 处理图表事件
const handleChartReady = (chart: any) => {
  emit('chartReady', chart);
};

const handleChartClick = (params: any) => {
  emit('chartClick', params);
};

const handleChartHover = (params: any) => {
  emit('chartHover', params);
};
</script>

<template>
  <BaseChart
    :option="chartOption"
    :width="width"
    :height="height"
    :loading="loading"
    @chart-ready="handleChartReady"
    @chart-click="handleChartClick"
    @chart-hover="handleChartHover"
  />
</template>
