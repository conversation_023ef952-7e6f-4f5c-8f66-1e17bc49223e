<script setup lang="ts">
import { ref } from 'vue';
import { NCard, NGrid, NGi, NNumberAnimation } from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';

const { SvgIconVNode } = useSvgIcon();

// 模拟数据，实际应从API获取
const statistics = ref([
  {
    title: '壁纸总数',
    value: 12580,
    icon: 'material-symbols:image-outline',
    color: '#18a058',
    suffix: '张',
    trend: 'up',
    trendValue: '12.5%'
  },
  {
    title: '今日上传',
    value: 86,
    icon: 'material-symbols:cloud-upload-outline',
    color: '#2080f0',
    suffix: '张',
    trend: 'up',
    trendValue: '8.2%'
  },
  {
    title: '总下载量',
    value: 358200,
    icon: 'material-symbols:download',
    color: '#f0a020',
    suffix: '次',
    trend: 'up',
    trendValue: '15.8%'
  },
  {
    title: '活跃用户',
    value: 5820,
    icon: 'material-symbols:group-outline',
    color: '#d03050',
    suffix: '人',
    trend: 'down',
    trendValue: '3.2%'
  }
]);
</script>

<template>
  <NGrid :x-gap="16" :y-gap="16" :cols="4" responsive="screen" item-responsive>
    <NGi v-for="item in statistics" :key="item.title" span="4 s:2 m:1">
      <NCard :bordered="false" class="h-120px">
        <div class="flex items-center justify-between">
          <div class="flex-1">
            <div class="text-16px text-gray-500 mb-8px">{{ item.title }}</div>
            <div class="flex items-baseline">
              <NNumberAnimation
                :from="0"
                :to="item.value"
                :duration="1000"
                show-separator
                class="text-24px font-bold"
              />
              <span class="text-14px text-gray-500 ml-4px">{{ item.suffix }}</span>
            </div>
            <div class="flex items-center mt-8px">
              <component
                :is="SvgIconVNode({
                  icon: item.trend === 'up' ? 'material-symbols:trending-up' : 'material-symbols:trending-down',
                  fontSize: 16
                })"
                :style="{ color: item.trend === 'up' ? '#18a058' : '#d03050' }"
              />
              <span
                class="text-12px ml-4px"
                :style="{ color: item.trend === 'up' ? '#18a058' : '#d03050' }"
              >
                {{ item.trendValue }}
              </span>
              <span class="text-12px text-gray-400 ml-4px">较昨日</span>
            </div>
          </div>
          <div class="w-48px h-48px rounded-8px flex items-center justify-center" :style="{ backgroundColor: `${item.color}20` }">
            <component
              :is="SvgIconVNode({ icon: item.icon, fontSize: 24 })"
              :style="{ color: item.color }"
            />
          </div>
        </div>
      </NCard>
    </NGi>
  </NGrid>
</template>
