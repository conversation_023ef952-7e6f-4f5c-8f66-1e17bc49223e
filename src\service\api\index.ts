// 统一导出所有API服务
export * from './auth';
export * from './route';
export * from './user';
export * from './permission';
export * from './analytics';

// 重新导出常用的API函数，方便使用
export {
  // 用户管理相关
  fetchUserList,
  fetchUserDetail,
  createUser,
  updateUser,
  deleteUser,
  batchOperateUsers,
  resetUserPassword,
  toggleUserBan,
  fetchUserLoginLogs,
  exportUserData,
  fetchUserStatistics,
  fetchUserActivityStats,
  generateMockUserData
} from './user';

export {
  // 权限管理相关
  fetchPermissionTree,
  fetchPermissionList,
  createPermission,
  updatePermission,
  deletePermission,
  fetchRoleList,
  fetchRoleDetail,
  createRole,
  updateRole,
  deleteRole,
  assignRolePermissions,
  fetchRolePermissions,
  assignUserRoles,
  fetchUserRoles,
  fetchUserPermissions,
  checkUserPermission,
  generateMockPermissionData,
  generateMockRoleData
} from './permission';

export {
  // 数据分析相关
  fetchOverviewStats,
  fetchDownloadAnalytics,
  fetchUserAnalytics,
  fetchSearchAnalytics,
  fetchTrafficAnalytics,
  exportAnalyticsReport,
  fetchRealTimeData,
  generateMockAnalyticsData
} from './analytics';

// API配置
export const API_CONFIG = {
  // 基础配置
  BASE_URL: import.meta.env.VITE_API_BASE_URL || '/api',
  TIMEOUT: 30000,

  // 分页配置
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,

  // 文件上传配置
  UPLOAD_MAX_SIZE: 10 * 1024 * 1024, // 10MB
  UPLOAD_ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],

  // 导出配置
  EXPORT_FORMATS: ['excel', 'csv', 'pdf'] as const,

  // 权限配置
  PERMISSION_CACHE_TIME: 5 * 60 * 1000, // 5分钟

  // 实时数据配置
  REALTIME_UPDATE_INTERVAL: 5000, // 5秒
};

// API响应状态码
export const API_STATUS = {
  SUCCESS: 200,
  CREATED: 201,
  NO_CONTENT: 204,
  BAD_REQUEST: 400,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  CONFLICT: 409,
  UNPROCESSABLE_ENTITY: 422,
  INTERNAL_SERVER_ERROR: 500,
  SERVICE_UNAVAILABLE: 503
} as const;

// API错误消息
export const API_MESSAGES = {
  NETWORK_ERROR: '网络连接失败，请检查网络设置',
  TIMEOUT_ERROR: '请求超时，请稍后重试',
  SERVER_ERROR: '服务器内部错误，请联系管理员',
  UNAUTHORIZED: '登录已过期，请重新登录',
  FORBIDDEN: '权限不足，无法执行此操作',
  NOT_FOUND: '请求的资源不存在',
  VALIDATION_ERROR: '数据验证失败，请检查输入内容',
  CONFLICT: '数据冲突，请刷新页面后重试'
} as const;

// 工具函数
export const apiUtils = {
  // 构建查询参数
  buildQueryParams: (params: Record<string, any>): string => {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value !== null && value !== undefined && value !== '') {
        if (Array.isArray(value)) {
          value.forEach(item => searchParams.append(key, String(item)));
        } else {
          searchParams.append(key, String(value));
        }
      }
    });
    return searchParams.toString();
  },

  // 格式化文件大小
  formatFileSize: (bytes: number): string => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 验证文件类型
  validateFileType: (file: File, allowedTypes: string[]): boolean => {
    return allowedTypes.includes(file.type);
  },

  // 验证文件大小
  validateFileSize: (file: File, maxSize: number): boolean => {
    return file.size <= maxSize;
  },

  // 生成下载链接
  generateDownloadUrl: (filename: string, data: any, type: string = 'application/json'): string => {
    const blob = new Blob([JSON.stringify(data, null, 2)], { type });
    const url = URL.createObjectURL(blob);
    return url;
  },

  // 下载文件
  downloadFile: (url: string, filename: string): void => {
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  },

  // 格式化日期
  formatDate: (date: string | Date, format: string = 'YYYY-MM-DD HH:mm:ss'): string => {
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return format
      .replace('YYYY', String(year))
      .replace('MM', month)
      .replace('DD', day)
      .replace('HH', hours)
      .replace('mm', minutes)
      .replace('ss', seconds);
  },

  // 防抖函数
  debounce: <T extends (...args: any[]) => any>(
    func: T,
    wait: number
  ): ((...args: Parameters<T>) => void) => {
    let timeout: NodeJS.Timeout;
    return (...args: Parameters<T>) => {
      clearTimeout(timeout);
      timeout = setTimeout(() => func.apply(null, args), wait);
    };
  },

  // 节流函数
  throttle: <T extends (...args: any[]) => any>(
    func: T,
    limit: number
  ): ((...args: Parameters<T>) => void) => {
    let inThrottle: boolean;
    return (...args: Parameters<T>) => {
      if (!inThrottle) {
        func.apply(null, args);
        inThrottle = true;
        setTimeout(() => inThrottle = false, limit);
      }
    };
  }
};

// Mock数据开关
export const MOCK_CONFIG = {
  ENABLED: import.meta.env.VITE_MOCK_ENABLED === 'true',
  DELAY: 500, // 模拟网络延迟
};

// 导出类型定义
export type {
  UserInfo,
  UserQueryParams,
  UserListResponse,
  BatchOperationParams
} from './user';

export type {
  Permission,
  Role,
  UserRole,
  PermissionQueryParams,
  RoleQueryParams
} from './permission';

export type {
  ChartDataPoint,
  PieChartData,
  OverviewStats,
  DownloadAnalytics,
  UserAnalytics,
  SearchAnalytics,
  TrafficAnalytics,
  TimeRange
} from './analytics';
