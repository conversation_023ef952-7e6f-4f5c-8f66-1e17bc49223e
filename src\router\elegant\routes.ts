/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'analytics',
    path: '/analytics',
    component: 'layout.base',
    meta: {
      title: '数据分析',
      icon: 'material-symbols:analytics',
      order: 7,
      i18nKey: 'route.analytics'
    },
    children: [
      {
        name: 'analytics_dashboard',
        path: '/analytics/dashboard',
        component: 'view.analytics_dashboard',
        meta: {
          title: 'analytics_dashboard',
          i18nKey: 'route.analytics_dashboard'
        }
      },
      {
        name: 'analytics_download',
        path: '/analytics/download',
        component: 'view.analytics_download',
        meta: {
          title: '下载统计',
          icon: 'material-symbols:download-done',
          i18nKey: 'route.analytics_download'
        }
      },
      {
        name: 'analytics_search',
        path: '/analytics/search',
        component: 'view.analytics_search',
        meta: {
          title: '搜索分析',
          icon: 'material-symbols:search-insights',
          i18nKey: 'route.analytics_search'
        }
      },
      {
        name: 'analytics_user',
        path: '/analytics/user',
        component: 'view.analytics_user',
        meta: {
          title: '用户分析',
          icon: 'material-symbols:person-search',
          i18nKey: 'route.analytics_user'
        }
      }
    ]
  },
  {
    name: 'banner',
    path: '/banner',
    component: 'layout.base$view.banner',
    meta: {
      title: 'Banner管理',
      icon: 'material-symbols:view-carousel',
      order: 5,
      i18nKey: 'route.banner'
    }
  },
  {
    name: 'category',
    path: '/category',
    component: 'layout.base$view.category',
    meta: {
      title: '分类管理',
      icon: 'material-symbols:category',
      order: 3,
      i18nKey: 'route.category'
    }
  },
  {
    name: 'collection',
    path: '/collection',
    component: 'layout.base$view.collection',
    meta: {
      title: '专题管理',
      icon: 'material-symbols:collections-bookmark',
      order: 4,
      i18nKey: 'route.collection'
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: '仪表盘',
      icon: 'mdi:monitor-dashboard',
      order: 1,
      i18nKey: 'route.home'
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true,
      keepAlive: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'settings',
    path: '/settings',
    component: 'layout.base',
    meta: {
      title: '系统设置',
      icon: 'material-symbols:settings',
      order: 8,
      i18nKey: 'route.settings'
    },
    children: [
      {
        name: 'settings_api',
        path: '/settings/api',
        component: 'view.settings_api',
        meta: {
          title: 'API管理',
          icon: 'material-symbols:api',
          i18nKey: 'route.settings_api'
        }
      },
      {
        name: 'settings_basic',
        path: '/settings/basic',
        component: 'view.settings_basic',
        meta: {
          title: '基础配置',
          icon: 'material-symbols:tune',
          i18nKey: 'route.settings_basic'
        }
      },
      {
        name: 'settings_storage',
        path: '/settings/storage',
        component: 'view.settings_storage',
        meta: {
          title: '存储管理',
          icon: 'material-symbols:storage',
          i18nKey: 'route.settings_storage'
        }
      }
    ]
  },
  {
    name: 'tag',
    path: '/tag',
    component: 'layout.base$view.tag',
    meta: {
      title: '标签管理',
      icon: 'material-symbols:label',
      order: 4,
      i18nKey: 'route.tag'
    }
  },
  {
    name: 'user',
    path: '/user',
    component: 'layout.base',
    meta: {
      title: '用户管理',
      icon: 'material-symbols:group',
      order: 6,
      i18nKey: 'route.user'
    },
    children: [
      {
        name: 'user_list',
        path: '/user/list',
        component: 'view.user_list',
        meta: {
          title: '用户列表',
          icon: 'material-symbols:person',
          i18nKey: 'route.user_list'
        }
      },
      {
        name: 'user_permission',
        path: '/user/permission',
        component: 'view.user_permission',
        meta: {
          title: '权限管理',
          icon: 'material-symbols:admin-panel-settings',
          i18nKey: 'route.user_permission'
        }
      }
    ]
  },
  {
    name: 'wallpaper',
    path: '/wallpaper',
    component: 'layout.base',
    meta: {
      title: '壁纸管理',
      icon: 'material-symbols:image',
      order: 2,
      i18nKey: 'route.wallpaper'
    },
    children: [
      {
        name: 'wallpaper_list',
        path: '/wallpaper/list',
        component: 'view.wallpaper_list',
        meta: {
          title: '壁纸列表',
          icon: 'material-symbols:photo-library',
          i18nKey: 'route.wallpaper_list'
        }
      },
      {
        name: 'wallpaper_upload',
        path: '/wallpaper/upload',
        component: 'view.wallpaper_upload',
        meta: {
          title: '上传壁纸',
          icon: 'material-symbols:cloud-upload',
          i18nKey: 'route.wallpaper_upload'
        }
      }
    ]
  }
];
