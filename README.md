<div align="center">
	<img src="./public/favicon.svg" width="160" />
	<h1>青云壁纸管理系统</h1>
  <span>中文 | <a href="./README.en_US.md">English</a></span>
</div>

---

[![license](https://img.shields.io/badge/license-MIT-green.svg)](./LICENSE)
![vue](https://img.shields.io/badge/vue-3.5.20-brightgreen.svg)
![vite](https://img.shields.io/badge/vite-7.1.3-brightgreen.svg)
![typescript](https://img.shields.io/badge/typescript-5.9.2-blue.svg)
![naiveui](https://img.shields.io/badge/naiveui-2.42.0-blue.svg)

> [!NOTE]
> 如果您觉得青云壁纸管理系统对您有所帮助，或者您喜欢我们的项目，请在 GitHub 上给我们一个 ⭐️。您的支持是我们持续改进和增加新功能的动力！感谢您的支持！

## 🌟 简介

**青云壁纸管理系统**是一个专业的壁纸内容管理平台，专为壁纸类网站和应用程序设计。本系统基于最新的前端技术栈构建，包括 Vue3、Vite7、TypeScript、Pinia 和 UnoCSS，提供了完整的壁纸管理解决方案。

系统采用现代化的设计理念，界面清新优雅，功能强大且易于使用。无论是管理海量壁纸资源，还是进行分类标签管理、用户权限控制，青云壁纸管理系统都能轻松应对。

## ✨ 特性

### 🎨 现代化设计
- **清新优雅的界面**：采用精心设计的UI组件，提供舒适的视觉体验
- **深色模式支持**：内置深色/浅色主题切换，保护您的眼睛
- **响应式布局**：完美适配各种设备，从桌面到移动端

### 🚀 技术栈
- **Vue 3.5+**：最新的 Vue 3 Composition API
- **Vite 7**：极速的开发体验
- **TypeScript 5**：类型安全，提升代码质量
- **Pinia**：新一代状态管理方案
- **UnoCSS**：原子化 CSS 引擎，高效便捷

### 📦 功能特点
- **壁纸管理**：支持批量上传、分类管理、标签系统
- **用户系统**：完整的用户权限管理体系
- **数据统计**：直观的数据可视化展示
- **国际化**：内置中英文支持，可扩展更多语言
- **主题配置**：丰富的主题定制选项
- **路由权限**：灵活的前后端路由权限控制

### 🛠️ 开发体验
- **代码规范**：集成 ESLint、Prettier，保证代码质量
- **自动化路由**：基于文件的路由自动生成
- **组件自动导入**：无需手动导入常用组件
- **Git Hooks**：提交代码自动进行格式化和类型检查

## 📸 预览

### 登录页面
清新简洁的登录界面，支持多种登录方式

### 控制台
数据概览一目了然，关键指标实时展示

### 壁纸管理
强大的壁纸管理功能，支持批量操作

### 主题配置
丰富的主题配置选项，打造个性化界面

## 🚀 快速开始

### 环境要求

- **Node.js**: >= 20.19.0
- **pnpm**: >= 10.5.0

### 安装步骤

1. **克隆项目**
```bash
git clone https://github.com/qingyun-wallpaper/admin.git
cd admin
```

2. **安装依赖**
```bash
pnpm install
```

3. **启动开发服务器**
```bash
pnpm dev
```

4. **构建生产版本**
```bash
pnpm build
```

### 常用命令

```bash
# 开发环境启动
pnpm dev

# 生产环境构建
pnpm build

# 预览构建结果
pnpm preview

# 代码格式化
pnpm lint

# 类型检查
pnpm typecheck

# 提交代码（使用规范化提交）
pnpm commit
```

## 📁 项目结构

```
qingyun-wallpaper-admin/
├── .vscode/                # VSCode 配置
├── public/                 # 静态资源
├── src/                    # 源代码
│   ├── assets/            # 资源文件
│   ├── components/        # 组件
│   ├── composables/       # 组合式函数
│   ├── constants/         # 常量定义
│   ├── hooks/             # 钩子函数
│   ├── layouts/           # 布局组件
│   ├── locales/           # 国际化文件
│   ├── router/            # 路由配置
│   ├── service/           # API 服务
│   ├── store/             # 状态管理
│   ├── styles/            # 全局样式
│   ├── typings/           # TypeScript 类型定义
│   ├── utils/             # 工具函数
│   └── views/             # 页面组件
├── .env                    # 环境变量
├── .eslintrc.js           # ESLint 配置
├── index.html             # HTML 模板
├── package.json           # 项目配置
├── tsconfig.json          # TypeScript 配置
├── uno.config.ts          # UnoCSS 配置
└── vite.config.ts         # Vite 配置
```

## 🤝 贡献指南

我们欢迎所有形式的贡献！如果您想为项目做出贡献，请：

1. Fork 本仓库
2. 创建您的特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启一个 Pull Request

### 提交规范

本项目使用规范化的提交信息，请使用以下命令进行提交：

```bash
pnpm commit
```

## 📄 开源协议

本项目基于 [MIT](./LICENSE) 协议开源，您可以自由使用、修改和分发本项目。

## 🙏 致谢

感谢所有为本项目做出贡献的开发者！

特别感谢以下开源项目：
- [Vue.js](https://vuejs.org/)
- [Vite](https://vitejs.dev/)
- [Naive UI](https://www.naiveui.com/)
- [UnoCSS](https://unocss.dev/)
- [Pinia](https://pinia.vuejs.org/)

## 📮 联系我们

- 邮箱：<EMAIL>
- GitHub Issues：[提交问题](https://github.com/qingyun-wallpaper/admin/issues)

## 🌐 相关链接

- [在线演示](https://admin.qingyun-wallpaper.com)
- [文档中心](https://docs.qingyun-wallpaper.com)
- [更新日志](./CHANGELOG.md)

---

<div align="center">
  <p>Made with ❤️ by 青云壁纸团队</p>
  <p>© 2024 QingYun Wallpaper. All rights reserved.</p>
</div>
