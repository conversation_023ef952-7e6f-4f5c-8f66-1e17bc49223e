<script setup lang="ts">
import { computed } from 'vue';
import BaseChart from './BaseChart.vue';
import type { EChartsOption } from 'echarts';

interface DataPoint {
  name: string;
  value: number;
}

interface SeriesData {
  name: string;
  data: DataPoint[];
  color?: string;
  stack?: string;
}

interface Props {
  data: SeriesData[];
  title?: string;
  subtitle?: string;
  direction?: 'vertical' | 'horizontal';
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  showDataZoom?: boolean;
  stacked?: boolean;
  height?: string | number;
  width?: string | number;
  loading?: boolean;
  colors?: string[];
  barWidth?: string | number;
  barMaxWidth?: string | number;
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'vertical',
  showGrid: true,
  showLegend: true,
  showTooltip: true,
  showDataZoom: false,
  stacked: false,
  height: '400px',
  width: '100%',
  loading: false,
  colors: () => ['#2080f0', '#18a058', '#f0a020', '#d03050', '#722ed1'],
  barWidth: 'auto',
  barMaxWidth: 50
});

const emit = defineEmits<{
  chartReady: [chart: any];
  chartClick: [params: any];
  chartHover: [params: any];
}>();

// 生成图表配置
const chartOption = computed<EChartsOption>(() => {
  const isHorizontal = props.direction === 'horizontal';
  const categories = props.data[0]?.data.map(item => item.name) || [];
  
  const option: EChartsOption = {
    color: props.colors,
    title: props.title ? {
      text: props.title,
      subtext: props.subtitle,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    } : undefined,
    tooltip: props.showTooltip ? {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: (params: any) => {
        if (Array.isArray(params)) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            result += `${param.marker}${param.seriesName}: ${param.value.toLocaleString()}<br/>`;
          });
          return result;
        }
        return `${params.axisValue}<br/>${params.marker}${params.seriesName}: ${params.value.toLocaleString()}`;
      }
    } : undefined,
    legend: props.showLegend ? {
      data: props.data.map(series => series.name),
      top: props.title ? '10%' : '5%'
    } : undefined,
    grid: props.showGrid ? {
      left: isHorizontal ? '15%' : '3%',
      right: '4%',
      bottom: props.showDataZoom ? '15%' : '3%',
      top: props.showLegend ? (props.title ? '20%' : '15%') : (props.title ? '15%' : '10%'),
      containLabel: true
    } : undefined,
    xAxis: {
      type: isHorizontal ? 'value' : 'category',
      data: isHorizontal ? undefined : categories,
      axisLine: {
        lineStyle: {
          color: '#e0e0e6'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        formatter: isHorizontal ? (value: number) => {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M';
          }
          if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K';
          }
          return value.toString();
        } : undefined
      },
      splitLine: isHorizontal ? {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      } : undefined
    },
    yAxis: {
      type: isHorizontal ? 'category' : 'value',
      data: isHorizontal ? categories : undefined,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        formatter: !isHorizontal ? (value: number) => {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M';
          }
          if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K';
          }
          return value.toString();
        } : undefined
      },
      splitLine: !isHorizontal ? {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      } : undefined
    },
    dataZoom: props.showDataZoom ? [
      {
        type: 'inside',
        start: 0,
        end: 100,
        [isHorizontal ? 'yAxisIndex' : 'xAxisIndex']: 0
      },
      {
        start: 0,
        end: 100,
        height: 30,
        [isHorizontal ? 'yAxisIndex' : 'xAxisIndex']: 0
      }
    ] : undefined,
    series: props.data.map((series, index) => ({
      name: series.name,
      type: 'bar',
      data: series.data.map(item => item.value),
      stack: props.stacked ? (series.stack || 'default') : undefined,
      barWidth: props.barWidth,
      barMaxWidth: props.barMaxWidth,
      itemStyle: {
        color: series.color || props.colors[index % props.colors.length],
        borderRadius: isHorizontal ? [0, 4, 4, 0] : [4, 4, 0, 0]
      },
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      },
      label: {
        show: false,
        position: isHorizontal ? 'right' : 'top',
        formatter: '{c}'
      }
    }))
  };

  return option;
});

// 处理图表事件
const handleChartReady = (chart: any) => {
  emit('chartReady', chart);
};

const handleChartClick = (params: any) => {
  emit('chartClick', params);
};

const handleChartHover = (params: any) => {
  emit('chartHover', params);
};
</script>

<template>
  <BaseChart
    :option="chartOption"
    :width="width"
    :height="height"
    :loading="loading"
    @chart-ready="handleChartReady"
    @chart-click="handleChartClick"
    @chart-hover="handleChartHover"
  />
</template>
