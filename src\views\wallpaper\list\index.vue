<script setup lang="ts">
import { ref, reactive, computed } from 'vue';
import { 
  NCard, NSpace, NButton, NInput, NSelect, NDatePicker, 
  NGrid, NGi, NImage, NTag, NDropdown, NPagination,
  NModal, NForm, NFormItem, NUpload, NInputGroup,
  NSwitch, NRadioGroup, NRadio, useMessage, useDialog
} from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';

const { SvgIconVNode } = useSvgIcon();
const message = useMessage();
const dialog = useDialog();

interface Wallpaper {
  id: number;
  title: string;
  description: string;
  url: string;
  thumbnail: string;
  category: string;
  tags: string[];
  resolution: string;
  fileSize: string;
  downloads: number;
  likes: number;
  status: 'online' | 'offline' | 'pending';
  uploadTime: string;
  uploader: string;
}

// 搜索表单
const searchForm = reactive({
  keyword: '',
  category: null,
  status: null,
  dateRange: null as [number, number] | null
});

// 分类选项
const categoryOptions = [
  { label: '全部分类', value: null },
  { label: '风景', value: 'landscape' },
  { label: '动漫', value: 'anime' },
  { label: '美女', value: 'beauty' },
  { label: '游戏', value: 'game' },
  { label: '汽车', value: 'car' },
  { label: '影视', value: 'movie' },
  { label: '动物', value: 'animal' },
  { label: '科技', value: 'tech' },
  { label: '其他', value: 'other' }
];

// 状态选项
const statusOptions = [
  { label: '全部状态', value: null },
  { label: '已上线', value: 'online' },
  { label: '已下线', value: 'offline' },
  { label: '待审核', value: 'pending' }
];

// 视图模式
const viewMode = ref<'grid' | 'list'>('grid');

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 12,
  total: 100
});

// 模拟数据
const wallpaperList = ref<Wallpaper[]>([
  {
    id: 1,
    title: '山水风景',
    description: '美丽的山水风景壁纸',
    url: 'https://picsum.photos/1920/1080?random=1',
    thumbnail: 'https://picsum.photos/400/300?random=1',
    category: 'landscape',
    tags: ['自然', '山水', '风景'],
    resolution: '1920x1080',
    fileSize: '2.3MB',
    downloads: 5680,
    likes: 1200,
    status: 'online',
    uploadTime: '2024-01-15 10:30:00',
    uploader: '张三'
  },
  {
    id: 2,
    title: '动漫少女',
    description: '可爱的动漫少女壁纸',
    url: 'https://picsum.photos/1920/1080?random=2',
    thumbnail: 'https://picsum.photos/400/300?random=2',
    category: 'anime',
    tags: ['动漫', '二次元', '少女'],
    resolution: '1920x1080',
    fileSize: '1.8MB',
    downloads: 4320,
    likes: 980,
    status: 'online',
    uploadTime: '2024-01-14 14:20:00',
    uploader: '李四'
  },
  {
    id: 3,
    title: '科技概念',
    description: '未来科技概念壁纸',
    url: 'https://picsum.photos/1920/1080?random=3',
    thumbnail: 'https://picsum.photos/400/300?random=3',
    category: 'tech',
    tags: ['科技', '未来', '概念'],
    resolution: '2560x1440',
    fileSize: '3.1MB',
    downloads: 3890,
    likes: 750,
    status: 'pending',
    uploadTime: '2024-01-13 09:15:00',
    uploader: '王五'
  },
  // 添加更多模拟数据...
]);

// 选中的壁纸
const selectedWallpapers = ref<number[]>([]);

// 编辑模态框
const editModalVisible = ref(false);
const editForm = ref<Partial<Wallpaper>>({});

// 操作下拉菜单
const getDropdownOptions = (wallpaper: Wallpaper) => [
  {
    label: '查看详情',
    key: 'view',
    icon: () => SvgIconVNode({ icon: 'material-symbols:visibility' })
  },
  {
    label: '编辑',
    key: 'edit',
    icon: () => SvgIconVNode({ icon: 'material-symbols:edit' })
  },
  {
    label: '下载',
    key: 'download',
    icon: () => SvgIconVNode({ icon: 'material-symbols:download' })
  },
  {
    type: 'divider',
    key: 'd1'
  },
  {
    label: wallpaper.status === 'online' ? '下线' : '上线',
    key: 'toggle-status',
    icon: () => SvgIconVNode({ 
      icon: wallpaper.status === 'online' ? 'material-symbols:cloud-off' : 'material-symbols:cloud-done' 
    })
  },
  {
    label: '删除',
    key: 'delete',
    icon: () => SvgIconVNode({ icon: 'material-symbols:delete' }),
    props: {
      style: { color: 'red' }
    }
  }
];

// 处理下拉菜单选择
const handleDropdownSelect = (key: string, wallpaper: Wallpaper) => {
  switch (key) {
    case 'view':
      handleView(wallpaper);
      break;
    case 'edit':
      handleEdit(wallpaper);
      break;
    case 'download':
      handleDownload(wallpaper);
      break;
    case 'toggle-status':
      handleToggleStatus(wallpaper);
      break;
    case 'delete':
      handleDelete(wallpaper);
      break;
  }
};

// 查看详情
const handleView = (wallpaper: Wallpaper) => {
  window.open(wallpaper.url, '_blank');
};

// 编辑
const handleEdit = (wallpaper: Wallpaper) => {
  editForm.value = { ...wallpaper };
  editModalVisible.value = true;
};

// 下载
const handleDownload = (wallpaper: Wallpaper) => {
  message.success(`开始下载：${wallpaper.title}`);
};

// 切换状态
const handleToggleStatus = (wallpaper: Wallpaper) => {
  const newStatus = wallpaper.status === 'online' ? '下线' : '上线';
  dialog.warning({
    title: '确认操作',
    content: `确定要${newStatus}「${wallpaper.title}」吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      wallpaper.status = wallpaper.status === 'online' ? 'offline' : 'online';
      message.success(`已${newStatus}`);
    }
  });
};

// 删除
const handleDelete = (wallpaper: Wallpaper) => {
  dialog.error({
    title: '确认删除',
    content: `确定要删除「${wallpaper.title}」吗？此操作不可恢复！`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = wallpaperList.value.findIndex(w => w.id === wallpaper.id);
      if (index > -1) {
        wallpaperList.value.splice(index, 1);
        message.success('删除成功');
      }
    }
  });
};

// 批量操作
const handleBatchOnline = () => {
  if (selectedWallpapers.value.length === 0) {
    message.warning('请先选择要操作的壁纸');
    return;
  }
  message.success(`已上线 ${selectedWallpapers.value.length} 张壁纸`);
  selectedWallpapers.value = [];
};

const handleBatchOffline = () => {
  if (selectedWallpapers.value.length === 0) {
    message.warning('请先选择要操作的壁纸');
    return;
  }
  message.success(`已下线 ${selectedWallpapers.value.length} 张壁纸`);
  selectedWallpapers.value = [];
};

const handleBatchDelete = () => {
  if (selectedWallpapers.value.length === 0) {
    message.warning('请先选择要删除的壁纸');
    return;
  }
  dialog.error({
    title: '批量删除',
    content: `确定要删除选中的 ${selectedWallpapers.value.length} 张壁纸吗？`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      message.success(`已删除 ${selectedWallpapers.value.length} 张壁纸`);
      selectedWallpapers.value = [];
    }
  });
};

// 搜索
const handleSearch = () => {
  message.info('搜索功能待实现');
};

// 重置
const handleReset = () => {
  searchForm.keyword = '';
  searchForm.category = null;
  searchForm.status = null;
  searchForm.dateRange = null;
};

// 获取状态标签类型
const getStatusType = (status: string) => {
  const map: Record<string, any> = {
    online: 'success',
    offline: 'default',
    pending: 'warning'
  };
  return map[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    online: '已上线',
    offline: '已下线',
    pending: '待审核'
  };
  return map[status] || '未知';
};
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 搜索区域 -->
    <NCard :bordered="false">
      <NSpace vertical>
        <NGrid :x-gap="16" :y-gap="16" :cols="4">
          <NGi>
            <NInput v-model:value="searchForm.keyword" placeholder="搜索壁纸标题、描述" clearable>
              <template #prefix>
                <component :is="SvgIconVNode({ icon: 'material-symbols:search' })" />
              </template>
            </NInput>
          </NGi>
          <NGi>
            <NSelect v-model:value="searchForm.category" :options="categoryOptions" placeholder="选择分类" />
          </NGi>
          <NGi>
            <NSelect v-model:value="searchForm.status" :options="statusOptions" placeholder="选择状态" />
          </NGi>
          <NGi>
            <NDatePicker v-model:value="searchForm.dateRange" type="daterange" clearable placeholder="选择日期范围" />
          </NGi>
        </NGrid>
        
        <NSpace justify="space-between">
          <NSpace>
            <NButton type="primary" @click="handleSearch">
              <template #icon>
                <component :is="SvgIconVNode({ icon: 'material-symbols:search' })" />
              </template>
              搜索
            </NButton>
            <NButton @click="handleReset">重置</NButton>
          </NSpace>
          
          <NSpace>
            <NButton type="info" @click="handleBatchOnline">批量上线</NButton>
            <NButton type="warning" @click="handleBatchOffline">批量下线</NButton>
            <NButton type="error" @click="handleBatchDelete">批量删除</NButton>
          </NSpace>
        </NSpace>
      </NSpace>
    </NCard>
    
    <!-- 操作栏 -->
    <NCard :bordered="false">
      <NSpace justify="space-between" align="center">
        <NSpace>
          <span class="text-16px font-medium">壁纸列表</span>
          <NTag type="info">共 {{ pagination.total }} 条</NTag>
        </NSpace>
        
        <NSpace>
          <NRadioGroup v-model:value="viewMode">
            <NButton :type="viewMode === 'grid' ? 'primary' : 'default'" @click="viewMode = 'grid'">
              <template #icon>
                <component :is="SvgIconVNode({ icon: 'material-symbols:grid-view' })" />
              </template>
              网格
            </NButton>
            <NButton :type="viewMode === 'list' ? 'primary' : 'default'" @click="viewMode = 'list'">
              <template #icon>
                <component :is="SvgIconVNode({ icon: 'material-symbols:list' })" />
              </template>
              列表
            </NButton>
          </NRadioGroup>
        </NSpace>
      </NSpace>
    </NCard>
    
    <!-- 壁纸列表 -->
    <NCard :bordered="false">
      <!-- 网格视图 -->
      <NGrid v-if="viewMode === 'grid'" :x-gap="16" :y-gap="16" :cols="4" responsive="screen" item-responsive>
        <NGi v-for="wallpaper in wallpaperList" :key="wallpaper.id" span="4 s:2 m:1">
          <div class="wallpaper-card">
            <div class="relative group">
              <NImage
                :src="wallpaper.thumbnail"
                :alt="wallpaper.title"
                class="w-full h-200px object-cover rounded-t-8px"
                lazy
              />
              <div class="absolute top-8px right-8px">
                <NTag :type="getStatusType(wallpaper.status)" size="small">
                  {{ getStatusText(wallpaper.status) }}
                </NTag>
              </div>
              <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-50 transition-all duration-300 rounded-t-8px flex items-center justify-center opacity-0 group-hover:opacity-100">
                <NSpace>
                  <NButton type="primary" size="small" @click="handleView(wallpaper)">查看</NButton>
                  <NButton type="info" size="small" @click="handleEdit(wallpaper)">编辑</NButton>
                  <NDropdown
                    :options="getDropdownOptions(wallpaper)"
                    @select="(key) => handleDropdownSelect(key, wallpaper)"
                  >
                    <NButton size="small">更多</NButton>
                  </NDropdown>
                </NSpace>
              </div>
            </div>
            
            <div class="p-12px">
              <div class="font-medium text-16px mb-4px truncate">{{ wallpaper.title }}</div>
              <div class="text-gray-500 text-12px mb-8px truncate">{{ wallpaper.description }}</div>
              
              <NSpace size="small" wrap>
                <NTag v-for="tag in wallpaper.tags.slice(0, 2)" :key="tag" size="small" :bordered="false">
                  {{ tag }}
                </NTag>
              </NSpace>
              
              <div class="flex items-center justify-between mt-8px text-12px text-gray-500">
                <span>{{ wallpaper.resolution }}</span>
                <NSpace size="small">
                  <span class="flex items-center">
                    <component :is="SvgIconVNode({ icon: 'material-symbols:download', fontSize: 14 })" class="mr-2px" />
                    {{ wallpaper.downloads }}
                  </span>
                  <span class="flex items-center">
                    <component :is="SvgIconVNode({ icon: 'material-symbols:favorite', fontSize: 14 })" class="mr-2px" />
                    {{ wallpaper.likes }}
                  </span>
                </NSpace>
              </div>
            </div>
          </div>
        </NGi>
      </NGrid>
      
      <!-- 列表视图 - 简化版本 -->
      <div v-else>
        列表视图待实现...
      </div>
      
      <!-- 分页 -->
      <div class="mt-16px flex justify-center">
        <NPagination
          v-model:page="pagination.page"
          :page-size="pagination.pageSize"
          :item-count="pagination.total"
          show-size-picker
          :page-sizes="[12, 24, 36, 48]"
          @update:page-size="(size) => pagination.pageSize = size"
        />
      </div>
    </NCard>
    
    <!-- 编辑模态框 -->
    <NModal v-model:show="editModalVisible" title="编辑壁纸" preset="card" style="width: 600px">
      <NForm :model="editForm" label-placement="left" label-width="80px">
        <NFormItem label="标题">
          <NInput v-model:value="editForm.title" />
        </NFormItem>
        <NFormItem label="描述">
          <NInput v-model:value="editForm.description" type="textarea" :rows="3" />
        </NFormItem>
        <NFormItem label="分类">
          <NSelect v-model:value="editForm.category" :options="categoryOptions.slice(1)" />
        </NFormItem>
        <NFormItem label="状态">
          <NRadioGroup v-model:value="editForm.status">
            <NRadio value="online">上线</NRadio>
            <NRadio value="offline">下线</NRadio>
            <NRadio value="pending">待审核</NRadio>
          </NRadioGroup>
        </NFormItem>
      </NForm>
      
      <template #footer>
        <NSpace justify="end">
          <NButton @click="editModalVisible = false">取消</NButton>
          <NButton type="primary" @click="() => { editModalVisible = false; message.success('保存成功'); }">
            保存
          </NButton>
        </NSpace>
      </template>
    </NModal>
  </NSpace>
</template>

<style scoped>
.wallpaper-card {
  border: 1px solid var(--n-border-color);
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.3s;
}

.wallpaper-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}
</style>
