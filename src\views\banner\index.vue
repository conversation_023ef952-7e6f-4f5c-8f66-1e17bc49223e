<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue';
import { 
  NCard, NSpace, NButton, NImage, NTag, NModal, NForm, NFormItem,
  NInput, NSelect, NSwitch, NInputNumber, useMessage, useDialog,
  NDataTable, NUpload, NUploadDragger, NIcon, NText, NRadioGroup, NRadio
} from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';

const { SvgIconVNode } = useSvgIcon();
const message = useMessage();
const dialog = useDialog();

interface Banner {
  id: number;
  title: string;
  image: string;
  link: string;
  linkType: 'internal' | 'external' | 'none';
  target: '_blank' | '_self';
  status: 'online' | 'offline';
  sort: number;
  startTime: string;
  endTime: string;
  clickCount: number;
  createTime: string;
  updateTime: string;
}

// Banner列表
const bannerList = ref<Banner[]>([
  {
    id: 1,
    title: '春节特惠活动',
    image: 'https://picsum.photos/800/300?random=21',
    link: '/collection/1',
    linkType: 'internal',
    target: '_self',
    status: 'online',
    sort: 1,
    startTime: '2024-01-01',
    endTime: '2024-02-01',
    clickCount: 12580,
    createTime: '2024-01-01',
    updateTime: '2024-01-15'
  },
  {
    id: 2,
    title: '新用户福利',
    image: 'https://picsum.photos/800/300?random=22',
    link: 'https://example.com/promotion',
    linkType: 'external',
    target: '_blank',
    status: 'online',
    sort: 2,
    startTime: '2024-01-05',
    endTime: '2024-12-31',
    clickCount: 8960,
    createTime: '2024-01-05',
    updateTime: '2024-01-14'
  },
  {
    id: 3,
    title: '热门壁纸推荐',
    image: 'https://picsum.photos/800/300?random=23',
    link: '/wallpaper/list',
    linkType: 'internal',
    target: '_self',
    status: 'online',
    sort: 3,
    startTime: '2024-01-10',
    endTime: '2024-06-30',
    clickCount: 6780,
    createTime: '2024-01-10',
    updateTime: '2024-01-13'
  },
  {
    id: 4,
    title: '会员专享',
    image: 'https://picsum.photos/800/300?random=24',
    link: '',
    linkType: 'none',
    target: '_self',
    status: 'offline',
    sort: 4,
    startTime: '2024-01-15',
    endTime: '2024-03-15',
    clickCount: 3450,
    createTime: '2024-01-15',
    updateTime: '2024-01-16'
  }
]);

// 编辑模态框
const editModalVisible = ref(false);
const editForm = ref<Partial<Banner>>({
  title: '',
  image: '',
  link: '',
  linkType: 'none',
  target: '_self',
  status: 'offline',
  sort: 999,
  startTime: '',
  endTime: ''
});
const isEdit = ref(false);

// 表格列定义
const columns: DataTableColumns<Banner> = [
  {
    title: '排序',
    key: 'sort',
    width: 80,
    align: 'center'
  },
  {
    title: 'Banner图片',
    key: 'image',
    width: 200,
    render(row) {
      return h(NImage, {
        src: row.image,
        width: 160,
        height: 60,
        objectFit: 'cover',
        style: 'border-radius: 4px'
      });
    }
  },
  {
    title: '标题',
    key: 'title',
    width: 150
  },
  {
    title: '链接类型',
    key: 'linkType',
    width: 100,
    render(row) {
      const typeMap = {
        internal: { label: '内部链接', type: 'info' },
        external: { label: '外部链接', type: 'warning' },
        none: { label: '无链接', type: 'default' }
      };
      const config = typeMap[row.linkType];
      return h(NTag, { type: config.type as any, size: 'small' }, { default: () => config.label });
    }
  },
  {
    title: '链接地址',
    key: 'link',
    width: 200,
    ellipsis: {
      tooltip: true
    },
    render(row) {
      return row.link || '-';
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render(row) {
      return h(NTag, {
        type: row.status === 'online' ? 'success' : 'default',
        size: 'small'
      }, { default: () => row.status === 'online' ? '已上线' : '已下线' });
    }
  },
  {
    title: '点击次数',
    key: 'clickCount',
    width: 100,
    render(row) {
      return row.clickCount.toLocaleString();
    }
  },
  {
    title: '有效期',
    key: 'time',
    width: 200,
    render(row) {
      return `${row.startTime} 至 ${row.endTime}`;
    }
  },
  {
    title: '操作',
    key: 'action',
    width: 200,
    fixed: 'right',
    render(row) {
      return h(NSpace, {}, {
        default: () => [
          h(NButton, {
            size: 'small',
            onClick: () => handleEdit(row)
          }, { default: () => '编辑' }),
          h(NButton, {
            size: 'small',
            type: row.status === 'online' ? 'warning' : 'success',
            onClick: () => handleToggleStatus(row)
          }, { default: () => row.status === 'online' ? '下线' : '上线' }),
          h(NButton, {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row)
          }, { default: () => '删除' })
        ]
      });
    }
  }
];

// 获取统计数据
const statistics = computed(() => {
  const total = bannerList.value.length;
  const online = bannerList.value.filter(b => b.status === 'online').length;
  const totalClicks = bannerList.value.reduce((sum, b) => sum + b.clickCount, 0);
  return { total, online, offline: total - online, totalClicks };
});

// 新增Banner
const handleAdd = () => {
  isEdit.value = false;
  editForm.value = {
    title: '',
    image: '',
    link: '',
    linkType: 'none',
    target: '_self',
    status: 'offline',
    sort: Math.max(...bannerList.value.map(b => b.sort), 0) + 1,
    startTime: new Date().toISOString().split('T')[0],
    endTime: ''
  };
  editModalVisible.value = true;
};

// 编辑Banner
const handleEdit = (banner: Banner) => {
  isEdit.value = true;
  editForm.value = { ...banner };
  editModalVisible.value = true;
};

// 删除Banner
const handleDelete = (banner: Banner) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除Banner「${banner.title}」吗？`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = bannerList.value.findIndex(b => b.id === banner.id);
      if (index > -1) {
        bannerList.value.splice(index, 1);
        message.success('删除成功');
      }
    }
  });
};

// 切换状态
const handleToggleStatus = (banner: Banner) => {
  const newStatus = banner.status === 'online' ? '下线' : '上线';
  dialog.warning({
    title: '确认操作',
    content: `确定要${newStatus}Banner「${banner.title}」吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      banner.status = banner.status === 'online' ? 'offline' : 'online';
      message.success(`已${newStatus}`);
    }
  });
};

// 保存
const handleSave = () => {
  if (!editForm.value.title) {
    message.error('请输入Banner标题');
    return;
  }
  if (!editForm.value.image) {
    message.error('请上传Banner图片');
    return;
  }
  if (!editForm.value.startTime || !editForm.value.endTime) {
    message.error('请选择有效期');
    return;
  }
  
  if (isEdit.value) {
    // 编辑
    const index = bannerList.value.findIndex(b => b.id === editForm.value.id);
    if (index > -1) {
      bannerList.value[index] = {
        ...bannerList.value[index],
        ...editForm.value,
        updateTime: new Date().toISOString().split('T')[0]
      } as Banner;
      message.success('更新成功');
    }
  } else {
    // 新增
    const newBanner: Banner = {
      id: Math.max(...bannerList.value.map(b => b.id), 0) + 1,
      title: editForm.value.title!,
      image: editForm.value.image!,
      link: editForm.value.link || '',
      linkType: editForm.value.linkType || 'none',
      target: editForm.value.target || '_self',
      status: editForm.value.status || 'offline',
      sort: editForm.value.sort || 999,
      startTime: editForm.value.startTime!,
      endTime: editForm.value.endTime!,
      clickCount: 0,
      createTime: new Date().toISOString().split('T')[0],
      updateTime: new Date().toISOString().split('T')[0]
    };
    bannerList.value.push(newBanner);
    bannerList.value.sort((a, b) => a.sort - b.sort);
    message.success('添加成功');
  }
  
  editModalVisible.value = false;
};

// 拖拽排序
const handleDragEnd = () => {
  // 更新排序值
  bannerList.value.forEach((banner, index) => {
    banner.sort = index + 1;
  });
  message.success('排序已更新');
};

// 刷新
const handleRefresh = () => {
  message.success('数据已刷新');
};
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 统计卡片 -->
    <NCard :bordered="false">
      <div class="grid grid-cols-4 gap-16px">
        <div class="text-center">
          <div class="text-24px font-bold">{{ statistics.total }}</div>
          <div class="text-gray-500 mt-4px">Banner总数</div>
        </div>
        <div class="text-center">
          <div class="text-24px font-bold text-green-500">{{ statistics.online }}</div>
          <div class="text-gray-500 mt-4px">已上线</div>
        </div>
        <div class="text-center">
          <div class="text-24px font-bold text-gray-500">{{ statistics.offline }}</div>
          <div class="text-gray-500 mt-4px">已下线</div>
        </div>
        <div class="text-center">
          <div class="text-24px font-bold text-blue-500">{{ statistics.totalClicks.toLocaleString() }}</div>
          <div class="text-gray-500 mt-4px">总点击量</div>
        </div>
      </div>
    </NCard>
    
    <!-- 操作按钮 -->
    <NCard :bordered="false">
      <NSpace>
        <NButton type="primary" @click="handleAdd">
          <template #icon>
            <component :is="SvgIconVNode({ icon: 'material-symbols:add' })" />
          </template>
          新增Banner
        </NButton>
        <NButton @click="handleRefresh">
          <template #icon>
            <component :is="SvgIconVNode({ icon: 'material-symbols:refresh' })" />
          </template>
          刷新
        </NButton>
      </NSpace>
    </NCard>
    
    <!-- Banner列表 -->
    <NCard :bordered="false" title="Banner列表">
      <NDataTable
        :columns="columns"
        :data="bannerList"
        :pagination="false"
        :bordered="false"
        :single-line="false"
        striped
      />
    </NCard>
    
    <!-- 编辑模态框 -->
    <NModal
      v-model:show="editModalVisible"
      :title="isEdit ? '编辑Banner' : '新增Banner'"
      preset="card"
      style="width: 600px"
    >
      <NForm :model="editForm" label-placement="left" label-width="100px">
        <NFormItem label="Banner标题" required>
          <NInput v-model:value="editForm.title" placeholder="请输入Banner标题" />
        </NFormItem>
        
        <NFormItem label="Banner图片" required>
          <NInput v-model:value="editForm.image" placeholder="请输入图片URL" />
          <div v-if="editForm.image" class="mt-8px">
            <NImage :src="editForm.image" width="200" height="75" object-fit="cover" />
          </div>
        </NFormItem>
        
        <NFormItem label="链接类型">
          <NRadioGroup v-model:value="editForm.linkType">
            <NRadio value="none">无链接</NRadio>
            <NRadio value="internal">内部链接</NRadio>
            <NRadio value="external">外部链接</NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <NFormItem v-if="editForm.linkType !== 'none'" label="链接地址">
          <NInput v-model:value="editForm.link" placeholder="请输入链接地址" />
        </NFormItem>
        
        <NFormItem v-if="editForm.linkType === 'external'" label="打开方式">
          <NRadioGroup v-model:value="editForm.target">
            <NRadio value="_self">当前窗口</NRadio>
            <NRadio value="_blank">新窗口</NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <NFormItem label="开始时间" required>
          <NInput v-model:value="editForm.startTime" type="date" />
        </NFormItem>
        
        <NFormItem label="结束时间" required>
          <NInput v-model:value="editForm.endTime" type="date" />
        </NFormItem>
        
        <NFormItem label="状态">
          <NRadioGroup v-model:value="editForm.status">
            <NRadio value="online">上线</NRadio>
            <NRadio value="offline">下线</NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <NFormItem label="排序">
          <NInputNumber v-model:value="editForm.sort" :min="0" placeholder="数字越小越靠前" />
        </NFormItem>
      </NForm>
      
      <template #footer>
        <NSpace justify="end">
          <NButton @click="editModalVisible = false">取消</NButton>
          <NButton type="primary" @click="handleSave">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </NSpace>
</template>

<style scoped>
.banner-item {
  cursor: move;
  transition: all 0.3s;
}

.banner-item:hover {
  background-color: #f5f5f5;
}
</style>
