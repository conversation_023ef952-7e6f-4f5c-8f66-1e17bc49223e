<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import type { ECOption } from '@/components/common/echarts-card.vue';

const chartRef = ref<HTMLDivElement>();
let chartInstance: echarts.ECharts | null = null;

const option: ECOption = {
  tooltip: {
    trigger: 'axis',
    axisPointer: {
      type: 'cross',
      label: {
        backgroundColor: '#6a7985'
      }
    }
  },
  legend: {
    data: ['上传数量', '下载数量']
  },
  grid: {
    left: '3%',
    right: '4%',
    bottom: '3%',
    containLabel: true
  },
  xAxis: {
    type: 'category',
    boundaryGap: false,
    data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
  },
  yAxis: {
    type: 'value'
  },
  series: [
    {
      name: '上传数量',
      type: 'line',
      stack: 'Total',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(32, 128, 240, 0.3)' },
            { offset: 1, color: 'rgba(32, 128, 240, 0)' }
          ]
        }
      },
      emphasis: {
        focus: 'series'
      },
      lineStyle: {
        color: '#2080f0',
        width: 2
      },
      itemStyle: {
        color: '#2080f0'
      },
      data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
      name: '下载数量',
      type: 'line',
      stack: 'Total',
      smooth: true,
      areaStyle: {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: 'rgba(24, 160, 88, 0.3)' },
            { offset: 1, color: 'rgba(24, 160, 88, 0)' }
          ]
        }
      },
      emphasis: {
        focus: 'series'
      },
      lineStyle: {
        color: '#18a058',
        width: 2
      },
      itemStyle: {
        color: '#18a058'
      },
      data: [820, 932, 901, 934, 1290, 1330, 1320]
    }
  ]
};

const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    chartInstance.setOption(option);
  }
};

const resizeChart = () => {
  chartInstance?.resize();
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', resizeChart);
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart);
  chartInstance?.dispose();
});
</script>

<template>
  <div ref="chartRef" class="w-full h-360px"></div>
</template>
