// 导出所有图表组件
export { default as BaseChart } from './BaseChart.vue';
export { default as LineChart } from './LineChart.vue';
export { default as <PERSON><PERSON>hart } from './PieChart.vue';
export { default as Bar<PERSON>hart } from './BarChart.vue';
export { default as StatCard } from './StatCard.vue';

// 导出类型定义
export interface ChartDataPoint {
  name: string;
  value: number;
  date?: string;
}

export interface SeriesData {
  name: string;
  data: ChartDataPoint[];
  color?: string;
  smooth?: boolean;
  area?: boolean;
  stack?: string;
}

export interface PieDataItem {
  name: string;
  value: number;
  color?: string;
}

export interface StatCardData {
  title: string;
  value: number;
  prefix?: string;
  suffix?: string;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: number;
  icon?: string;
  iconColor?: string;
}

// 图表配置预设
export const chartPresets = {
  // 颜色主题
  colors: {
    primary: ['#2080f0', '#18a058', '#f0a020', '#d03050', '#722ed1'],
    business: ['#1890ff', '#52c41a', '#faad14', '#f5222d', '#722ed1'],
    gradient: ['#667eea', '#764ba2', '#f093fb', '#f5576c', '#4facfe'],
    pastel: ['#a8e6cf', '#dcedc1', '#ffd3a5', '#fd9853', '#ff8a80']
  },
  
  // 通用配置
  common: {
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      top: '10%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(50, 50, 50, 0.9)',
      borderColor: 'transparent',
      textStyle: {
        color: '#fff'
      }
    },
    legend: {
      top: '5%',
      textStyle: {
        fontSize: 12
      }
    }
  },
  
  // 线图预设
  line: {
    smooth: true,
    symbol: 'circle',
    symbolSize: 6,
    lineStyle: {
      width: 2
    },
    areaStyle: {
      opacity: 0.3
    }
  },
  
  // 柱状图预设
  bar: {
    barWidth: '60%',
    itemStyle: {
      borderRadius: [4, 4, 0, 0]
    }
  },
  
  // 饼图预设
  pie: {
    radius: '70%',
    center: ['50%', '50%'],
    roseType: false,
    label: {
      show: true,
      position: 'outside'
    }
  }
};

// 工具函数
export const chartUtils = {
  // 格式化数值
  formatValue: (value: number, type: 'number' | 'percentage' | 'currency' = 'number') => {
    switch (type) {
      case 'percentage':
        return `${value.toFixed(1)}%`;
      case 'currency':
        return `¥${value.toLocaleString()}`;
      default:
        if (value >= 1000000) {
          return `${(value / 1000000).toFixed(1)}M`;
        }
        if (value >= 1000) {
          return `${(value / 1000).toFixed(1)}K`;
        }
        return value.toLocaleString();
    }
  },
  
  // 生成渐变色
  generateGradient: (color: string, direction: 'vertical' | 'horizontal' = 'vertical') => {
    const isVertical = direction === 'vertical';
    return {
      type: 'linear',
      x: 0,
      y: 0,
      x2: isVertical ? 0 : 1,
      y2: isVertical ? 1 : 0,
      colorStops: [
        { offset: 0, color },
        { offset: 1, color: 'rgba(255, 255, 255, 0.1)' }
      ]
    };
  },
  
  // 生成时间序列数据
  generateTimeSeriesData: (days: number, baseValue: number, variance: number) => {
    const data: ChartDataPoint[] = [];
    const now = new Date();
    
    for (let i = days; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 3600 * 1000);
      const value = baseValue + Math.random() * variance - variance / 2;
      data.push({
        name: date.toISOString().split('T')[0],
        value: Math.max(0, Math.floor(value)),
        date: date.toISOString().split('T')[0]
      });
    }
    
    return data;
  },
  
  // 生成饼图数据
  generatePieData: (categories: string[], total: number): PieDataItem[] => {
    const data: PieDataItem[] = [];
    let remaining = total;
    
    categories.forEach((name, index) => {
      const isLast = index === categories.length - 1;
      const value = isLast ? remaining : Math.floor(Math.random() * remaining * 0.4);
      
      data.push({
        name,
        value
      });
      
      remaining -= value;
    });
    
    return data;
  },
  
  // 计算增长率
  calculateGrowthRate: (current: number, previous: number): number => {
    if (previous === 0) return 0;
    return ((current - previous) / previous) * 100;
  },
  
  // 获取趋势方向
  getTrendDirection: (growthRate: number): 'up' | 'down' | 'stable' => {
    if (growthRate > 1) return 'up';
    if (growthRate < -1) return 'down';
    return 'stable';
  }
};

// 响应式断点
export const breakpoints = {
  xs: 480,
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
  xxl: 1600
};

// 图表主题配置
export const chartThemes = {
  light: {
    backgroundColor: '#ffffff',
    textColor: '#333333',
    axisLineColor: '#e0e0e6',
    splitLineColor: '#f0f0f0',
    gridColor: '#f5f5f5'
  },
  dark: {
    backgroundColor: '#1a1a1a',
    textColor: '#ffffff',
    axisLineColor: '#484848',
    splitLineColor: '#2a2a2a',
    gridColor: '#2a2a2a'
  }
};

// 动画配置
export const animationConfig = {
  duration: 1000,
  easing: 'cubicOut',
  delay: (idx: number) => idx * 100,
  animationDurationUpdate: 300,
  animationEasingUpdate: 'cubicInOut'
};
