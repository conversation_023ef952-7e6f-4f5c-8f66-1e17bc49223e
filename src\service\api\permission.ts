import { request } from '../request';

/**
 * 权限管理相关API
 */

// 权限类型枚举
export enum PermissionType {
  MENU = 'menu',
  BUTTON = 'button',
  API = 'api',
  DATA = 'data'
}

// 权限状态枚举
export enum PermissionStatus {
  ENABLED = 1,
  DISABLED = 0
}

// 权限数据类型
export interface Permission {
  id: string;
  name: string;
  code: string;
  type: PermissionType;
  parentId?: string;
  path?: string;
  component?: string;
  icon?: string;
  sort: number;
  status: PermissionStatus;
  description?: string;
  createTime: string;
  updateTime: string;
  children?: Permission[];
}

// 角色数据类型
export interface Role {
  id: string;
  name: string;
  code: string;
  description?: string;
  status: PermissionStatus;
  permissions: string[];
  userCount: number;
  createTime: string;
  updateTime: string;
}

// 用户角色关联
export interface UserRole {
  userId: string;
  roleId: string;
  assignTime: string;
  assignBy: string;
}

// 权限查询参数
export interface PermissionQueryParams {
  name?: string;
  type?: PermissionType;
  status?: PermissionStatus;
  parentId?: string;
}

// 角色查询参数
export interface RoleQueryParams {
  page: number;
  pageSize: number;
  name?: string;
  status?: PermissionStatus;
}

/**
 * 获取权限树
 */
export function fetchPermissionTree(params?: PermissionQueryParams) {
  return request<Permission[]>({
    url: '/permission/tree',
    method: 'get',
    params
  });
}

/**
 * 获取权限列表
 */
export function fetchPermissionList(params?: PermissionQueryParams) {
  return request<Permission[]>({
    url: '/permission/list',
    method: 'get',
    params
  });
}

/**
 * 创建权限
 */
export function createPermission(data: Partial<Permission>) {
  return request<{ id: string }>({
    url: '/permission/create',
    method: 'post',
    data
  });
}

/**
 * 更新权限
 */
export function updatePermission(id: string, data: Partial<Permission>) {
  return request<boolean>({
    url: `/permission/update/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除权限
 */
export function deletePermission(id: string) {
  return request<boolean>({
    url: `/permission/delete/${id}`,
    method: 'delete'
  });
}

/**
 * 获取角色列表
 */
export function fetchRoleList(params: RoleQueryParams) {
  return request<{
    list: Role[];
    total: number;
    page: number;
    pageSize: number;
  }>({
    url: '/role/list',
    method: 'get',
    params
  });
}

/**
 * 获取角色详情
 */
export function fetchRoleDetail(id: string) {
  return request<Role>({
    url: `/role/detail/${id}`,
    method: 'get'
  });
}

/**
 * 创建角色
 */
export function createRole(data: Partial<Role>) {
  return request<{ id: string }>({
    url: '/role/create',
    method: 'post',
    data
  });
}

/**
 * 更新角色
 */
export function updateRole(id: string, data: Partial<Role>) {
  return request<boolean>({
    url: `/role/update/${id}`,
    method: 'put',
    data
  });
}

/**
 * 删除角色
 */
export function deleteRole(id: string) {
  return request<boolean>({
    url: `/role/delete/${id}`,
    method: 'delete'
  });
}

/**
 * 分配角色权限
 */
export function assignRolePermissions(roleId: string, permissionIds: string[]) {
  return request<boolean>({
    url: `/role/assign-permissions/${roleId}`,
    method: 'post',
    data: { permissionIds }
  });
}

/**
 * 获取角色权限
 */
export function fetchRolePermissions(roleId: string) {
  return request<string[]>({
    url: `/role/permissions/${roleId}`,
    method: 'get'
  });
}

/**
 * 分配用户角色
 */
export function assignUserRoles(userId: string, roleIds: string[]) {
  return request<boolean>({
    url: `/user/assign-roles/${userId}`,
    method: 'post',
    data: { roleIds }
  });
}

/**
 * 获取用户角色
 */
export function fetchUserRoles(userId: string) {
  return request<Role[]>({
    url: `/user/roles/${userId}`,
    method: 'get'
  });
}

/**
 * 获取用户权限
 */
export function fetchUserPermissions(userId: string) {
  return request<Permission[]>({
    url: `/user/permissions/${userId}`,
    method: 'get'
  });
}

/**
 * 检查用户权限
 */
export function checkUserPermission(userId: string, permissionCode: string) {
  return request<boolean>({
    url: `/user/check-permission/${userId}`,
    method: 'get',
    params: { permissionCode }
  });
}

// Mock数据生成器
export function generateMockPermissionData(): Permission[] {
  return [
    {
      id: '1',
      name: '系统管理',
      code: 'system',
      type: PermissionType.MENU,
      path: '/system',
      icon: 'carbon:settings',
      sort: 1,
      status: PermissionStatus.ENABLED,
      description: '系统管理模块',
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00',
      children: [
        {
          id: '1-1',
          name: '用户管理',
          code: 'system:user',
          type: PermissionType.MENU,
          parentId: '1',
          path: '/system/user',
          component: 'system/user/index',
          icon: 'carbon:user-multiple',
          sort: 1,
          status: PermissionStatus.ENABLED,
          createTime: '2024-01-01 10:00:00',
          updateTime: '2024-01-01 10:00:00',
          children: [
            {
              id: '1-1-1',
              name: '用户查询',
              code: 'system:user:query',
              type: PermissionType.BUTTON,
              parentId: '1-1',
              sort: 1,
              status: PermissionStatus.ENABLED,
              createTime: '2024-01-01 10:00:00',
              updateTime: '2024-01-01 10:00:00'
            },
            {
              id: '1-1-2',
              name: '用户新增',
              code: 'system:user:add',
              type: PermissionType.BUTTON,
              parentId: '1-1',
              sort: 2,
              status: PermissionStatus.ENABLED,
              createTime: '2024-01-01 10:00:00',
              updateTime: '2024-01-01 10:00:00'
            },
            {
              id: '1-1-3',
              name: '用户编辑',
              code: 'system:user:edit',
              type: PermissionType.BUTTON,
              parentId: '1-1',
              sort: 3,
              status: PermissionStatus.ENABLED,
              createTime: '2024-01-01 10:00:00',
              updateTime: '2024-01-01 10:00:00'
            },
            {
              id: '1-1-4',
              name: '用户删除',
              code: 'system:user:delete',
              type: PermissionType.BUTTON,
              parentId: '1-1',
              sort: 4,
              status: PermissionStatus.ENABLED,
              createTime: '2024-01-01 10:00:00',
              updateTime: '2024-01-01 10:00:00'
            }
          ]
        },
        {
          id: '1-2',
          name: '角色管理',
          code: 'system:role',
          type: PermissionType.MENU,
          parentId: '1',
          path: '/system/role',
          component: 'system/role/index',
          icon: 'carbon:user-role',
          sort: 2,
          status: PermissionStatus.ENABLED,
          createTime: '2024-01-01 10:00:00',
          updateTime: '2024-01-01 10:00:00'
        }
      ]
    },
    {
      id: '2',
      name: '内容管理',
      code: 'content',
      type: PermissionType.MENU,
      path: '/content',
      icon: 'carbon:document',
      sort: 2,
      status: PermissionStatus.ENABLED,
      description: '内容管理模块',
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00',
      children: [
        {
          id: '2-1',
          name: '壁纸管理',
          code: 'content:wallpaper',
          type: PermissionType.MENU,
          parentId: '2',
          path: '/content/wallpaper',
          component: 'content/wallpaper/index',
          icon: 'carbon:image',
          sort: 1,
          status: PermissionStatus.ENABLED,
          createTime: '2024-01-01 10:00:00',
          updateTime: '2024-01-01 10:00:00'
        }
      ]
    }
  ];
}

export function generateMockRoleData(): Role[] {
  return [
    {
      id: '1',
      name: '超级管理员',
      code: 'super_admin',
      description: '拥有系统所有权限',
      status: PermissionStatus.ENABLED,
      permissions: ['1', '1-1', '1-1-1', '1-1-2', '1-1-3', '1-1-4', '1-2', '2', '2-1'],
      userCount: 1,
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00'
    },
    {
      id: '2',
      name: '管理员',
      code: 'admin',
      description: '拥有部分管理权限',
      status: PermissionStatus.ENABLED,
      permissions: ['1-1', '1-1-1', '1-1-2', '1-1-3', '2', '2-1'],
      userCount: 5,
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00'
    },
    {
      id: '3',
      name: '普通用户',
      code: 'user',
      description: '基础用户权限',
      status: PermissionStatus.ENABLED,
      permissions: ['2', '2-1'],
      userCount: 1000,
      createTime: '2024-01-01 10:00:00',
      updateTime: '2024-01-01 10:00:00'
    }
  ];
}
