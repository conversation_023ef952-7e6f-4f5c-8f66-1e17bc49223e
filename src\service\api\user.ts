import { request } from '../request';

/**
 * 用户管理相关API
 */

// 用户状态枚举
export enum UserStatus {
  ACTIVE = 1,
  INACTIVE = 0,
  BANNED = -1
}

// 用户角色枚举
export enum UserRole {
  SUPER_ADMIN = 'super_admin',
  ADMIN = 'admin',
  VIP = 'vip',
  USER = 'user'
}

// 用户数据类型
export interface UserInfo {
  id: string;
  username: string;
  email: string;
  phone: string;
  nickname: string;
  role: UserRole;
  status: UserStatus;
  avatar: string;
  registerTime: string;
  lastLoginTime: string;
  downloadCount: number;
  uploadCount: number;
  collectCount: number;
  loginCount: number;
  lastLoginIp: string;
  deviceInfo: string;
  vipExpireTime?: string;
  tags: string[];
}

// 用户查询参数
export interface UserQueryParams {
  page: number;
  pageSize: number;
  keyword?: string;
  status?: UserStatus;
  role?: UserRole;
  registerTimeRange?: [string, string];
}

// 用户列表响应
export interface UserListResponse {
  list: UserInfo[];
  total: number;
  page: number;
  pageSize: number;
}

// 批量操作参数
export interface BatchOperationParams {
  userIds: string[];
  action: 'enable' | 'disable' | 'delete' | 'export';
}

/**
 * 获取用户列表
 */
export function fetchUserList(params: UserQueryParams) {
  return request<UserListResponse>({
    url: '/user/list',
    method: 'get',
    params
  });
}

/**
 * 获取用户详情
 */
export function fetchUserDetail(userId: string) {
  return request<UserInfo>({
    url: `/user/detail/${userId}`,
    method: 'get'
  });
}

/**
 * 创建用户
 */
export function createUser(userData: Partial<UserInfo>) {
  return request<{ id: string }>({
    url: '/user/create',
    method: 'post',
    data: userData
  });
}

/**
 * 更新用户信息
 */
export function updateUser(userId: string, userData: Partial<UserInfo>) {
  return request<boolean>({
    url: `/user/update/${userId}`,
    method: 'put',
    data: userData
  });
}

/**
 * 删除用户
 */
export function deleteUser(userId: string) {
  return request<boolean>({
    url: `/user/delete/${userId}`,
    method: 'delete'
  });
}

/**
 * 批量操作用户
 */
export function batchOperateUsers(params: BatchOperationParams) {
  return request<boolean>({
    url: '/user/batch-operation',
    method: 'post',
    data: params
  });
}

/**
 * 重置用户密码
 */
export function resetUserPassword(userId: string) {
  return request<boolean>({
    url: `/user/reset-password/${userId}`,
    method: 'post'
  });
}

/**
 * 封禁/解封用户
 */
export function toggleUserBan(userId: string, banned: boolean) {
  return request<boolean>({
    url: `/user/toggle-ban/${userId}`,
    method: 'post',
    data: { banned }
  });
}

/**
 * 获取用户登录日志
 */
export function fetchUserLoginLogs(userId: string, params: { page: number; pageSize: number }) {
  return request<{
    list: Array<{
      id: string;
      loginTime: string;
      loginIp: string;
      deviceInfo: string;
      location: string;
      success: boolean;
    }>;
    total: number;
  }>({
    url: `/user/login-logs/${userId}`,
    method: 'get',
    params
  });
}

/**
 * 导出用户数据
 */
export function exportUserData(userIds?: string[]) {
  return request<{ downloadUrl: string }>({
    url: '/user/export',
    method: 'post',
    data: { userIds }
  });
}

/**
 * 获取用户统计数据
 */
export function fetchUserStatistics() {
  return request<{
    totalUsers: number;
    activeUsers: number;
    vipUsers: number;
    newUsersToday: number;
    newUsersThisWeek: number;
    newUsersThisMonth: number;
  }>({
    url: '/user/statistics',
    method: 'get'
  });
}

/**
 * 获取用户活动统计
 */
export function fetchUserActivityStats(userId: string) {
  return request<{
    downloadHistory: Array<{ date: string; count: number }>;
    uploadHistory: Array<{ date: string; count: number }>;
    loginHistory: Array<{ date: string; count: number }>;
  }>({
    url: `/user/activity-stats/${userId}`,
    method: 'get'
  });
}

// Mock数据生成器（开发阶段使用）
export function generateMockUserData(): UserInfo[] {
  const mockUsers: UserInfo[] = [];
  const roles = [UserRole.USER, UserRole.VIP, UserRole.ADMIN, UserRole.SUPER_ADMIN];
  const statuses = [UserStatus.ACTIVE, UserStatus.INACTIVE, UserStatus.BANNED];
  const tags = ['新用户', '活跃用户', 'VIP', '高价值用户', '违规用户', '测试用户'];

  for (let i = 1; i <= 50; i++) {
    const role = roles[Math.floor(Math.random() * roles.length)];
    const status = statuses[Math.floor(Math.random() * statuses.length)];
    const userTags = tags.slice(0, Math.floor(Math.random() * 3) + 1);

    mockUsers.push({
      id: i.toString(),
      username: `user${i.toString().padStart(3, '0')}`,
      email: `user${i}@example.com`,
      phone: `1${(3000000000 + i * 1000000).toString()}`,
      nickname: `用户${i}`,
      role,
      status,
      avatar: `https://api.dicebear.com/7.x/avataaars/svg?seed=user${i}`,
      registerTime: new Date(Date.now() - Math.random() * 365 * 24 * 3600 * 1000).toLocaleString(),
      lastLoginTime: new Date(Date.now() - Math.random() * 30 * 24 * 3600 * 1000).toLocaleString(),
      downloadCount: Math.floor(Math.random() * 1000),
      uploadCount: Math.floor(Math.random() * 100),
      collectCount: Math.floor(Math.random() * 500),
      loginCount: Math.floor(Math.random() * 500),
      lastLoginIp: `192.168.1.${100 + i}`,
      deviceInfo: ['Chrome 120.0 / Windows 10', 'Safari 17.0 / macOS', 'Firefox 121.0 / Linux'][Math.floor(Math.random() * 3)],
      vipExpireTime: role === UserRole.VIP ? '2024-12-31 23:59:59' : undefined,
      tags: userTags
    });
  }

  return mockUsers;
}
