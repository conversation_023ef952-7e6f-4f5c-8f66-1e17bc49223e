<script setup lang="ts">
import { ref } from 'vue';
import { NCard, NTimeline, NTimelineItem, NTag, NSpace, NAvatar, NTime } from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';

const { SvgIconVNode } = useSvgIcon();

interface Activity {
  id: number;
  type: 'upload' | 'download' | 'comment' | 'like' | 'user';
  user: string;
  avatar?: string;
  action: string;
  target?: string;
  time: number;
  icon: string;
  color: string;
}

// 模拟数据
const activities = ref<Activity[]>([
  {
    id: 1,
    type: 'upload',
    user: '张三',
    avatar: 'https://i.pravatar.cc/40?img=1',
    action: '上传了新壁纸',
    target: '星空夜景',
    time: Date.now() - 1000 * 60 * 5, // 5分钟前
    icon: 'material-symbols:cloud-upload',
    color: '#2080f0'
  },
  {
    id: 2,
    type: 'download',
    user: '李四',
    avatar: 'https://i.pravatar.cc/40?img=2',
    action: '下载了壁纸',
    target: '动漫少女',
    time: Date.now() - 1000 * 60 * 15, // 15分钟前
    icon: 'material-symbols:download',
    color: '#18a058'
  },
  {
    id: 3,
    type: 'comment',
    user: '王五',
    avatar: 'https://i.pravatar.cc/40?img=3',
    action: '评论了壁纸',
    target: '山水风景',
    time: Date.now() - 1000 * 60 * 30, // 30分钟前
    icon: 'material-symbols:comment',
    color: '#f0a020'
  },
  {
    id: 4,
    type: 'like',
    user: '赵六',
    avatar: 'https://i.pravatar.cc/40?img=4',
    action: '收藏了壁纸',
    target: '科技概念',
    time: Date.now() - 1000 * 60 * 45, // 45分钟前
    icon: 'material-symbols:favorite',
    color: '#d03050'
  },
  {
    id: 5,
    type: 'user',
    user: '新用户小明',
    avatar: 'https://i.pravatar.cc/40?img=5',
    action: '注册成为会员',
    time: Date.now() - 1000 * 60 * 60, // 1小时前
    icon: 'material-symbols:person-add',
    color: '#722ed1'
  },
  {
    id: 6,
    type: 'upload',
    user: '设计师小红',
    avatar: 'https://i.pravatar.cc/40?img=6',
    action: '批量上传了',
    target: '10张壁纸',
    time: Date.now() - 1000 * 60 * 120, // 2小时前
    icon: 'material-symbols:cloud-upload',
    color: '#2080f0'
  }
]);

const getActivityTypeTag = (type: Activity['type']) => {
  const typeMap = {
    upload: { label: '上传', type: 'info' },
    download: { label: '下载', type: 'success' },
    comment: { label: '评论', type: 'warning' },
    like: { label: '收藏', type: 'error' },
    user: { label: '用户', type: 'default' }
  };
  return typeMap[type] || { label: '其他', type: 'default' };
};
</script>

<template>
  <NCard :bordered="false" title="最近活动" class="card-wrapper">
    <template #header-extra>
      <NTag type="info" size="small">实时更新</NTag>
    </template>
    
    <NTimeline>
      <NTimelineItem
        v-for="activity in activities"
        :key="activity.id"
        :type="getActivityTypeTag(activity.type).type as any"
      >
        <template #icon>
          <div
            class="w-32px h-32px rounded-full flex items-center justify-center"
            :style="{ backgroundColor: `${activity.color}20` }"
          >
            <component
              :is="SvgIconVNode({ icon: activity.icon, fontSize: 18 })"
              :style="{ color: activity.color }"
            />
          </div>
        </template>
        
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <div class="flex items-center gap-8px mb-4px">
              <NAvatar
                :src="activity.avatar"
                :size="24"
                round
              />
              <span class="font-medium text-14px">{{ activity.user }}</span>
              <NTag :type="getActivityTypeTag(activity.type).type as any" size="small" :bordered="false">
                {{ getActivityTypeTag(activity.type).label }}
              </NTag>
            </div>
            <div class="text-14px text-gray-600">
              {{ activity.action }}
              <span v-if="activity.target" class="font-medium text-primary">「{{ activity.target }}」</span>
            </div>
          </div>
          <NTime :time="activity.time" type="relative" class="text-12px text-gray-400" />
        </div>
      </NTimelineItem>
    </NTimeline>
  </NCard>
</template>

<style scoped>
:deep(.n-timeline-item-content) {
  padding-top: 2px !important;
}
</style>
