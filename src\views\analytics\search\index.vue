<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue';
import { NCard, NSpace, NDatePicker, NButton, NGrid, NGridItem, NStatistic, NDataTable, NTag } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import * as echarts from 'echarts';

const timeRange = ref<[number, number] | null>(null);

// 统计数据
const statistics = reactive({
  total: 89234,
  today: 892,
  avgTime: 2.3,
  successRate: 95.6
});

// 热门搜索词数据
const hotKeywords = ref([
  { rank: 1, keyword: '风景', count: 5234, trend: 15.2 },
  { rank: 2, keyword: '动物', count: 4567, trend: -3.5 },
  { rank: 3, keyword: '科技', count: 3890, trend: 8.7 },
  { rank: 4, keyword: '美女', count: 3456, trend: 12.1 },
  { rank: 5, keyword: '汽车', count: 2987, trend: -6.3 },
  { rank: 6, keyword: '自然', count: 2654, trend: 5.8 },
  { rank: 7, keyword: '建筑', count: 2345, trend: 9.2 },
  { rank: 8, keyword: '动漫', count: 2123, trend: 18.6 },
  { rank: 9, keyword: '游戏', count: 1987, trend: -2.4 },
  { rank: 10, keyword: '抽象', count: 1654, trend: 7.3 }
]);

// 表格列配置
const columns: DataTableColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    align: 'center',
    render(row: any) {
      const colors = ['#f50', '#fa8c16', '#faad14'];
      if (row.rank <= 3) {
        return h(NTag, { 
          type: 'error',
          round: true,
          size: 'small'
        }, { default: () => row.rank });
      }
      return row.rank;
    }
  },
  {
    title: '搜索词',
    key: 'keyword',
    render(row: any) {
      return h('span', { class: 'font-medium' }, row.keyword);
    }
  },
  {
    title: '搜索次数',
    key: 'count',
    width: 120,
    sorter: (a: any, b: any) => a.count - b.count
  },
  {
    title: '趋势',
    key: 'trend',
    width: 100,
    render(row: any) {
      const isUp = row.trend > 0;
      return h('span', {
        class: isUp ? 'text-green-500' : 'text-red-500'
      }, `${isUp ? '↑' : '↓'} ${Math.abs(row.trend)}%`);
    }
  }
];

// 初始化搜索趋势图表
const initTrendChart = () => {
  const chartDom = document.getElementById('search-trend-chart');
  if (!chartDom) return;
  
  const myChart = echarts.init(chartDom);
  const option = {
    title: {
      text: '搜索趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['搜索次数']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00', '24:00']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '搜索次数',
        type: 'line',
        smooth: true,
        data: [320, 280, 450, 890, 1200, 980, 560],
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(32, 128, 240, 0.3)' },
            { offset: 1, color: 'rgba(32, 128, 240, 0)' }
          ])
        },
        itemStyle: {
          color: '#2080f0'
        }
      }
    ]
  };
  myChart.setOption(option);
  
  // 响应式
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

// 初始化搜索来源图表
const initSourceChart = () => {
  const chartDom = document.getElementById('search-source-chart');
  if (!chartDom) return;
  
  const myChart = echarts.init(chartDom);
  const option = {
    title: {
      text: '搜索来源分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '搜索来源',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: [
          { value: 45234, name: 'APP搜索' },
          { value: 23456, name: '首页推荐' },
          { value: 12345, name: '分类浏览' },
          { value: 8234, name: '标签搜索' }
        ]
      }
    ]
  };
  myChart.setOption(option);
  
  // 响应式
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

onMounted(() => {
  setTimeout(() => {
    initTrendChart();
    initSourceChart();
  }, 300);
});

// 搜索
const handleSearch = () => {
  console.log('搜索分析');
};

// 导出
const handleExport = () => {
  console.log('导出数据');
};
</script>

<template>
  <div class="space-y-4">
    <!-- 统计卡片 -->
    <NGrid :cols="4" :x-gap="16">
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总搜索量" :value="statistics.total">
            <template #prefix>
              <SvgIcon icon="carbon:search" class="text-20px text-primary" />
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="今日搜索" :value="statistics.today">
            <template #suffix>
              <span class="text-xs text-green-500">↑18.2%</span>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="平均用时" :value="statistics.avgTime" suffix="s" />
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="搜索成功率" :value="statistics.successRate" suffix="%" />
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 主内容区 -->
    <NCard :bordered="false">
      <!-- 搜索栏 -->
      <template #header>
        <div class="flex justify-between items-center">
          <div class="text-lg font-medium">搜索分析</div>
          <NSpace>
            <NDatePicker 
              v-model:value="timeRange" 
              type="daterange" 
              clearable
              placeholder="选择时间范围"
            />
            <NButton type="primary" @click="handleSearch">
              <template #icon>
                <SvgIcon icon="carbon:search" />
              </template>
              查询
            </NButton>
            <NButton @click="handleExport">
              <template #icon>
                <SvgIcon icon="carbon:export" />
              </template>
              导出
            </NButton>
          </NSpace>
        </div>
      </template>

      <!-- 图表区域 -->
      <NGrid :cols="2" :x-gap="16" :y-gap="16">
        <NGridItem>
          <NCard title="搜索趋势" :bordered="false" size="small">
            <div id="search-trend-chart" style="height: 300px"></div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard title="搜索来源" :bordered="false" size="small">
            <div id="search-source-chart" style="height: 300px"></div>
          </NCard>
        </NGridItem>
      </NGrid>

      <!-- 热门搜索词 -->
      <NCard title="热门搜索词 TOP10" :bordered="false" size="small" class="mt-4">
        <NDataTable 
          :columns="columns" 
          :data="hotKeywords"
          :pagination="false"
        />
      </NCard>
    </NCard>
  </div>
</template>
