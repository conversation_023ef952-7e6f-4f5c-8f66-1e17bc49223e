<script setup lang="ts">
import { computed } from 'vue';
import BaseChart from './BaseChart.vue';
import type { EChartsOption } from 'echarts';

interface DataPoint {
  name: string;
  value: number;
  date?: string;
}

interface SeriesData {
  name: string;
  data: DataPoint[];
  color?: string;
  smooth?: boolean;
  area?: boolean;
}

interface Props {
  data: SeriesData[];
  title?: string;
  subtitle?: string;
  xAxisType?: 'category' | 'time' | 'value';
  yAxisType?: 'value' | 'log';
  showGrid?: boolean;
  showLegend?: boolean;
  showTooltip?: boolean;
  showDataZoom?: boolean;
  height?: string | number;
  width?: string | number;
  loading?: boolean;
  smooth?: boolean;
  area?: boolean;
  colors?: string[];
}

const props = withDefaults(defineProps<Props>(), {
  xAxisType: 'category',
  yAxisType: 'value',
  showGrid: true,
  showLegend: true,
  showTooltip: true,
  showDataZoom: false,
  height: '400px',
  width: '100%',
  loading: false,
  smooth: false,
  area: false,
  colors: () => ['#2080f0', '#18a058', '#f0a020', '#d03050', '#722ed1']
});

const emit = defineEmits<{
  chartReady: [chart: any];
  chartClick: [params: any];
  chartHover: [params: any];
}>();

// 生成图表配置
const chartOption = computed<EChartsOption>(() => {
  const option: EChartsOption = {
    color: props.colors,
    title: props.title ? {
      text: props.title,
      subtext: props.subtitle,
      left: 'center',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold'
      }
    } : undefined,
    tooltip: props.showTooltip ? {
      trigger: 'axis',
      axisPointer: {
        type: 'cross',
        label: {
          backgroundColor: '#6a7985'
        }
      },
      formatter: (params: any) => {
        if (Array.isArray(params)) {
          let result = `${params[0].axisValue}<br/>`;
          params.forEach((param: any) => {
            result += `${param.marker}${param.seriesName}: ${param.value.toLocaleString()}<br/>`;
          });
          return result;
        }
        return `${params.axisValue}<br/>${params.marker}${params.seriesName}: ${params.value.toLocaleString()}`;
      }
    } : undefined,
    legend: props.showLegend ? {
      data: props.data.map(series => series.name),
      top: props.title ? '10%' : '5%'
    } : undefined,
    grid: props.showGrid ? {
      left: '3%',
      right: '4%',
      bottom: props.showDataZoom ? '15%' : '3%',
      top: props.showLegend ? (props.title ? '20%' : '15%') : (props.title ? '15%' : '10%'),
      containLabel: true
    } : undefined,
    xAxis: {
      type: props.xAxisType,
      data: props.xAxisType === 'category' ? props.data[0]?.data.map(item => item.name) : undefined,
      boundaryGap: false,
      axisLine: {
        lineStyle: {
          color: '#e0e0e6'
        }
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666'
      }
    },
    yAxis: {
      type: props.yAxisType,
      axisLine: {
        show: false
      },
      axisTick: {
        show: false
      },
      axisLabel: {
        color: '#666',
        formatter: (value: number) => {
          if (value >= 1000000) {
            return (value / 1000000).toFixed(1) + 'M';
          }
          if (value >= 1000) {
            return (value / 1000).toFixed(1) + 'K';
          }
          return value.toString();
        }
      },
      splitLine: {
        lineStyle: {
          color: '#f0f0f0',
          type: 'dashed'
        }
      }
    },
    dataZoom: props.showDataZoom ? [
      {
        type: 'inside',
        start: 0,
        end: 100
      },
      {
        start: 0,
        end: 100,
        height: 30
      }
    ] : undefined,
    series: props.data.map((series, index) => ({
      name: series.name,
      type: 'line',
      data: series.data.map(item => item.value),
      smooth: series.smooth ?? props.smooth,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: series.color || props.colors[index % props.colors.length]
      },
      itemStyle: {
        color: series.color || props.colors[index % props.colors.length],
        borderWidth: 2,
        borderColor: '#fff'
      },
      areaStyle: (series.area ?? props.area) ? {
        color: {
          type: 'linear',
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            {
              offset: 0,
              color: series.color || props.colors[index % props.colors.length]
            },
            {
              offset: 1,
              color: 'rgba(255, 255, 255, 0.1)'
            }
          ]
        }
      } : undefined,
      emphasis: {
        focus: 'series',
        itemStyle: {
          shadowBlur: 10,
          shadowColor: 'rgba(0, 0, 0, 0.3)'
        }
      }
    }))
  };

  return option;
});

// 处理图表事件
const handleChartReady = (chart: any) => {
  emit('chartReady', chart);
};

const handleChartClick = (params: any) => {
  emit('chartClick', params);
};

const handleChartHover = (params: any) => {
  emit('chartHover', params);
};
</script>

<template>
  <BaseChart
    :option="chartOption"
    :width="width"
    :height="height"
    :loading="loading"
    @chart-ready="handleChartReady"
    @chart-click="handleChartClick"
    @chart-hover="handleChartHover"
  />
</template>
