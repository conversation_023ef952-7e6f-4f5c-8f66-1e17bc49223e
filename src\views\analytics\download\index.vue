<script setup lang="ts">
import { ref, reactive, onMounted, h } from 'vue';
import { NCard, NSpace, NDatePicker, NButton, NSelect, NGrid, NGridItem, NStatistic, NDataTable } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import * as echarts from 'echarts';

const timeRange = ref<[number, number] | null>(null);
const selectedType = ref('all');

const typeOptions = [
  { label: '全部', value: 'all' },
  { label: '壁纸', value: 'wallpaper' },
  { label: '专题', value: 'collection' }
];

// 统计数据
const statistics = reactive({
  total: 128956,
  today: 1289,
  week: 8956,
  month: 45678
});

// 热门下载数据
const hotDownloads = ref([
  { rank: 1, title: '春天风景壁纸', category: '风景', downloads: 15234, trend: 12.5 },
  { rank: 2, title: '科技未来城市', category: '科技', downloads: 12856, trend: -5.2 },
  { rank: 3, title: '可爱猫咪合集', category: '动物', downloads: 10234, trend: 8.9 },
  { rank: 4, title: '极简主义设计', category: '设计', downloads: 9876, trend: 15.3 },
  { rank: 5, title: '宇宙星空壁纸', category: '自然', downloads: 8765, trend: -2.1 }
]);

// 表格列配置
const columns: DataTableColumns = [
  {
    title: '排名',
    key: 'rank',
    width: 80,
    align: 'center'
  },
  {
    title: '壁纸名称',
    key: 'title',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '分类',
    key: 'category',
    width: 100
  },
  {
    title: '下载量',
    key: 'downloads',
    width: 120,
    sorter: (a: any, b: any) => a.downloads - b.downloads
  },
  {
    title: '趋势',
    key: 'trend',
    width: 100,
    render(row: any) {
      const isUp = row.trend > 0;
      return h('span', {
        class: isUp ? 'text-green-500' : 'text-red-500'
      }, `${isUp ? '↑' : '↓'} ${Math.abs(row.trend)}%`);
    }
  }
];

// 初始化下载趋势图表
const initTrendChart = () => {
  const chartDom = document.getElementById('trend-chart');
  if (!chartDom) return;
  
  const myChart = echarts.init(chartDom);
  const option = {
    title: {
      text: '下载趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['下载量']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '下载量',
        type: 'line',
        smooth: true,
        data: [1200, 1800, 1500, 2300, 2100, 2500, 2200],
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(24, 160, 88, 0.3)' },
            { offset: 1, color: 'rgba(24, 160, 88, 0)' }
          ])
        },
        itemStyle: {
          color: '#18a058'
        }
      }
    ]
  };
  myChart.setOption(option);
  
  // 响应式
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

// 初始化分类分布图表
const initCategoryChart = () => {
  const chartDom = document.getElementById('category-chart');
  if (!chartDom) return;
  
  const myChart = echarts.init(chartDom);
  const option = {
    title: {
      text: '分类下载分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '下载量',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35234, name: '风景' },
          { value: 28456, name: '动物' },
          { value: 23489, name: '科技' },
          { value: 18765, name: '设计' },
          { value: 15234, name: '自然' },
          { value: 7778, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  myChart.setOption(option);
  
  // 响应式
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

onMounted(() => {
  setTimeout(() => {
    initTrendChart();
    initCategoryChart();
  }, 300);
});

// 搜索
const handleSearch = () => {
  console.log('搜索下载统计');
};

// 导出
const handleExport = () => {
  console.log('导出数据');
};
</script>

<template>
  <div class="space-y-4">
    <!-- 统计卡片 -->
    <NGrid :cols="4" :x-gap="16">
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总下载量" :value="statistics.total">
            <template #prefix>
              <SvgIcon icon="carbon:download" class="text-20px text-primary" />
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="今日下载" :value="statistics.today">
            <template #suffix>
              <span class="text-xs text-green-500">↑12.5%</span>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="本周下载" :value="statistics.week" />
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="本月下载" :value="statistics.month" />
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 主内容区 -->
    <NCard :bordered="false">
      <!-- 搜索栏 -->
      <template #header>
        <div class="flex justify-between items-center">
          <div class="text-lg font-medium">下载统计分析</div>
          <NSpace>
            <NSelect 
              v-model:value="selectedType" 
              :options="typeOptions" 
              style="width: 120px"
            />
            <NDatePicker 
              v-model:value="timeRange" 
              type="daterange" 
              clearable
              placeholder="选择时间范围"
            />
            <NButton type="primary" @click="handleSearch">
              <template #icon>
                <SvgIcon icon="carbon:search" />
              </template>
              查询
            </NButton>
            <NButton @click="handleExport">
              <template #icon>
                <SvgIcon icon="carbon:export" />
              </template>
              导出
            </NButton>
          </NSpace>
        </div>
      </template>

      <!-- 图表区域 -->
      <NGrid :cols="2" :x-gap="16" :y-gap="16">
        <NGridItem>
          <NCard title="下载趋势" :bordered="false" size="small">
            <div id="trend-chart" style="height: 300px"></div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard title="分类分布" :bordered="false" size="small">
            <div id="category-chart" style="height: 300px"></div>
          </NCard>
        </NGridItem>
      </NGrid>

      <!-- 热门下载列表 -->
      <NCard title="热门下载排行" :bordered="false" size="small" class="mt-4">
        <NDataTable 
          :columns="columns" 
          :data="hotDownloads"
          :pagination="false"
        />
      </NCard>
    </NCard>
  </div>
</template>
