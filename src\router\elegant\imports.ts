/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  analytics_dashboard: () => import("@/views/analytics/dashboard/index.vue"),
  analytics_download: () => import("@/views/analytics/download/index.vue"),
  analytics: () => import("@/views/analytics/index.vue"),
  analytics_search: () => import("@/views/analytics/search/index.vue"),
  analytics_user: () => import("@/views/analytics/user/index.vue"),
  banner: () => import("@/views/banner/index.vue"),
  category: () => import("@/views/category/index.vue"),
  collection: () => import("@/views/collection/index.vue"),
  home: () => import("@/views/home/<USER>"),
  settings_api: () => import("@/views/settings/api/index.vue"),
  settings_basic: () => import("@/views/settings/basic/index.vue"),
  settings_storage: () => import("@/views/settings/storage/index.vue"),
  tag: () => import("@/views/tag/index.vue"),
  user_list: () => import("@/views/user/list/index.vue"),
  user_permission: () => import("@/views/user/permission/index.vue"),
  wallpaper_list: () => import("@/views/wallpaper/list/index.vue"),
  wallpaper_upload: () => import("@/views/wallpaper/upload/index.vue"),
};
