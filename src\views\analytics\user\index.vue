<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { NCard, NSpace, NDatePicker, NButton, NSelect, NGrid, NGridItem, NStatistic, NProgress } from 'naive-ui';
import * as echarts from 'echarts';

const timeRange = ref<[number, number] | null>(null);
const selectedDimension = ref('all');

const dimensionOptions = [
  { label: '全部', value: 'all' },
  { label: '地区', value: 'region' },
  { label: '设备', value: 'device' },
  { label: '渠道', value: 'channel' }
];

// 统计数据
const statistics = reactive({
  total: 156789,
  newUsers: 1234,
  activeUsers: 45678,
  retention: 68.5
});

// 用户画像数据
const userProfile = reactive({
  gender: [
    { name: '男性', value: 58, color: '#2080f0' },
    { name: '女性', value: 42, color: '#f5317f' }
  ],
  age: [
    { name: '18岁以下', value: 12 },
    { name: '18-24岁', value: 28 },
    { name: '25-30岁', value: 35 },
    { name: '31-40岁', value: 18 },
    { name: '40岁以上', value: 7 }
  ],
  device: [
    { name: 'iOS', value: 45 },
    { name: 'Android', value: 38 },
    { name: 'Web', value: 12 },
    { name: '其他', value: 5 }
  ]
});

// 初始化用户增长图表
const initGrowthChart = () => {
  const chartDom = document.getElementById('user-growth-chart');
  if (!chartDom) return;
  
  const myChart = echarts.init(chartDom);
  const option = {
    title: {
      text: '用户增长趋势'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['新增用户', '活跃用户']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '新增用户',
        type: 'line',
        smooth: true,
        data: [120, 182, 191, 234, 290, 330, 310],
        itemStyle: {
          color: '#18a058'
        }
      },
      {
        name: '活跃用户',
        type: 'line',
        smooth: true,
        data: [820, 932, 901, 934, 1290, 1330, 1320],
        itemStyle: {
          color: '#2080f0'
        }
      }
    ]
  };
  myChart.setOption(option);
  
  // 响应式
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

// 初始化活跃度图表
const initActivityChart = () => {
  const chartDom = document.getElementById('user-activity-chart');
  if (!chartDom) return;
  
  const myChart = echarts.init(chartDom);
  const option = {
    title: {
      text: '用户活跃度分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: ['0-2小时', '2-6小时', '6-12小时', '12-24小时', '24小时以上']
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: '用户数',
        type: 'bar',
        data: [2345, 5678, 3456, 1234, 567],
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#2080f0' },
            { offset: 1, color: '#18a058' }
          ])
        }
      }
    ]
  };
  myChart.setOption(option);
  
  // 响应式
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

// 初始化地域分布图表
const initRegionChart = () => {
  const chartDom = document.getElementById('user-region-chart');
  if (!chartDom) return;
  
  const myChart = echarts.init(chartDom);
  const option = {
    title: {
      text: '用户地域分布',
      left: 'center'
    },
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        name: '用户数',
        type: 'pie',
        radius: '50%',
        data: [
          { value: 35234, name: '北京' },
          { value: 28456, name: '上海' },
          { value: 23489, name: '广州' },
          { value: 18765, name: '深圳' },
          { value: 15234, name: '成都' },
          { value: 12345, name: '杭州' },
          { value: 9876, name: '武汉' },
          { value: 8765, name: '其他' }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  };
  myChart.setOption(option);
  
  // 响应式
  window.addEventListener('resize', () => {
    myChart.resize();
  });
};

onMounted(() => {
  setTimeout(() => {
    initGrowthChart();
    initActivityChart();
    initRegionChart();
  }, 300);
});

// 搜索
const handleSearch = () => {
  console.log('用户分析');
};

// 导出
const handleExport = () => {
  console.log('导出数据');
};
</script>

<template>
  <div class="space-y-4">
    <!-- 统计卡片 -->
    <NGrid :cols="4" :x-gap="16">
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总用户数" :value="statistics.total">
            <template #prefix>
              <SvgIcon icon="carbon:user-multiple" class="text-20px text-primary" />
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="今日新增" :value="statistics.newUsers">
            <template #suffix>
              <span class="text-xs text-green-500">↑15.6%</span>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="活跃用户" :value="statistics.activeUsers" />
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="留存率" :value="statistics.retention" suffix="%" />
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 主内容区 -->
    <NCard :bordered="false">
      <!-- 搜索栏 -->
      <template #header>
        <div class="flex justify-between items-center">
          <div class="text-lg font-medium">用户分析</div>
          <NSpace>
            <NSelect 
              v-model:value="selectedDimension" 
              :options="dimensionOptions" 
              style="width: 120px"
            />
            <NDatePicker 
              v-model:value="timeRange" 
              type="daterange" 
              clearable
              placeholder="选择时间范围"
            />
            <NButton type="primary" @click="handleSearch">
              <template #icon>
                <SvgIcon icon="carbon:search" />
              </template>
              查询
            </NButton>
            <NButton @click="handleExport">
              <template #icon>
                <SvgIcon icon="carbon:export" />
              </template>
              导出
            </NButton>
          </NSpace>
        </div>
      </template>

      <!-- 图表区域 -->
      <NGrid :cols="3" :x-gap="16" :y-gap="16">
        <NGridItem>
          <NCard title="用户增长" :bordered="false" size="small">
            <div id="user-growth-chart" style="height: 300px"></div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard title="活跃度分布" :bordered="false" size="small">
            <div id="user-activity-chart" style="height: 300px"></div>
          </NCard>
        </NGridItem>
        <NGridItem>
          <NCard title="地域分布" :bordered="false" size="small">
            <div id="user-region-chart" style="height: 300px"></div>
          </NCard>
        </NGridItem>
      </NGrid>

      <!-- 用户画像 -->
      <NCard title="用户画像" :bordered="false" size="small" class="mt-4">
        <NGrid :cols="3" :x-gap="16">
          <NGridItem>
            <div class="space-y-4">
              <div class="text-sm font-medium text-gray-600">性别分布</div>
              <div class="space-y-3">
                <div v-for="item in userProfile.gender" :key="item.name" class="flex items-center gap-3">
                  <span class="w-12 text-sm">{{ item.name }}</span>
                  <NProgress
                    type="line"
                    :percentage="item.value"
                    :color="item.color"
                    :show-indicator="false"
                    style="flex: 1"
                  />
                  <span class="text-sm">{{ item.value }}%</span>
                </div>
              </div>
            </div>
          </NGridItem>
          <NGridItem>
            <div class="space-y-4">
              <div class="text-sm font-medium text-gray-600">年龄分布</div>
              <div class="space-y-3">
                <div v-for="item in userProfile.age" :key="item.name" class="flex items-center gap-3">
                  <span class="w-20 text-sm">{{ item.name }}</span>
                  <NProgress
                    type="line"
                    :percentage="item.value"
                    :show-indicator="false"
                    style="flex: 1"
                  />
                  <span class="text-sm">{{ item.value }}%</span>
                </div>
              </div>
            </div>
          </NGridItem>
          <NGridItem>
            <div class="space-y-4">
              <div class="text-sm font-medium text-gray-600">设备分布</div>
              <div class="space-y-3">
                <div v-for="item in userProfile.device" :key="item.name" class="flex items-center gap-3">
                  <span class="w-16 text-sm">{{ item.name }}</span>
                  <NProgress
                    type="line"
                    :percentage="item.value"
                    :show-indicator="false"
                    style="flex: 1"
                  />
                  <span class="text-sm">{{ item.value }}%</span>
                </div>
              </div>
            </div>
          </NGridItem>
        </NGrid>
      </NCard>
    </NCard>
  </div>
</template>
