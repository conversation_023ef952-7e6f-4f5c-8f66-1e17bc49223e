<script setup lang="ts">
import { ref } from 'vue';
import { NCard, NImage, NSpace, NTag, NBadge, NGrid, NGi } from 'naive-ui';

interface Wallpaper {
  id: number;
  title: string;
  thumbnail: string;
  downloads: number;
  category: string;
  uploadTime: string;
  isNew?: boolean;
  isHot?: boolean;
}

// 模拟数据
const popularWallpapers = ref<Wallpaper[]>([
  {
    id: 1,
    title: '山水风景',
    thumbnail: 'https://picsum.photos/300/200?random=1',
    downloads: 5680,
    category: '风景',
    uploadTime: '2024-01-15',
    isHot: true
  },
  {
    id: 2,
    title: '动漫美少女',
    thumbnail: 'https://picsum.photos/300/200?random=2',
    downloads: 4320,
    category: '动漫',
    uploadTime: '2024-01-14',
    isNew: true
  },
  {
    id: 3,
    title: '科技概念',
    thumbnail: 'https://picsum.photos/300/200?random=3',
    downloads: 3890,
    category: '科技',
    uploadTime: '2024-01-13'
  },
  {
    id: 4,
    title: '城市夜景',
    thumbnail: 'https://picsum.photos/300/200?random=4',
    downloads: 3560,
    category: '城市',
    uploadTime: '2024-01-12',
    isHot: true
  },
  {
    id: 5,
    title: '萌宠猫咪',
    thumbnail: 'https://picsum.photos/300/200?random=5',
    downloads: 3210,
    category: '动物',
    uploadTime: '2024-01-11'
  },
  {
    id: 6,
    title: '游戏CG',
    thumbnail: 'https://picsum.photos/300/200?random=6',
    downloads: 2980,
    category: '游戏',
    uploadTime: '2024-01-10',
    isNew: true
  }
]);
</script>

<template>
  <NCard :bordered="false" title="热门壁纸" class="card-wrapper">
    <template #header-extra>
      <NTag type="info" size="small">Top 6</NTag>
    </template>
    
    <NGrid :x-gap="16" :y-gap="16" :cols="3" responsive="screen" item-responsive>
      <NGi v-for="wallpaper in popularWallpapers" :key="wallpaper.id" span="3 s:3 m:1">
        <div class="wallpaper-item relative cursor-pointer group">
          <!-- 图片容器 -->
          <div class="relative overflow-hidden rounded-8px">
            <NBadge v-if="wallpaper.isHot" value="HOT" color="#ff4757" :offset="[-10, 10]">
              <NBadge v-if="wallpaper.isNew" value="NEW" color="#18a058" :offset="[-10, 35]">
                <NImage
                  :src="wallpaper.thumbnail"
                  :alt="wallpaper.title"
                  class="w-full h-140px object-cover transition-transform duration-300 group-hover:scale-110"
                  lazy
                />
              </NBadge>
            </NBadge>
            <NBadge v-else-if="wallpaper.isNew" value="NEW" color="#18a058" :offset="[-10, 10]">
              <NImage
                :src="wallpaper.thumbnail"
                :alt="wallpaper.title"
                class="w-full h-140px object-cover transition-transform duration-300 group-hover:scale-110"
                lazy
              />
            </NBadge>
            <NImage
              v-else
              :src="wallpaper.thumbnail"
              :alt="wallpaper.title"
              class="w-full h-140px object-cover transition-transform duration-300 group-hover:scale-110"
              lazy
            />
            
            <!-- 悬浮遮罩 -->
            <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-300 flex items-center justify-center">
              <span class="text-white opacity-0 group-hover:opacity-100 transition-opacity duration-300">查看详情</span>
            </div>
          </div>
          
          <!-- 信息区域 -->
          <div class="mt-8px">
            <div class="font-medium text-14px truncate">{{ wallpaper.title }}</div>
            <div class="flex items-center justify-between mt-4px">
              <NTag size="small" :bordered="false">{{ wallpaper.category }}</NTag>
              <span class="text-12px text-gray-500">{{ wallpaper.downloads }} 下载</span>
            </div>
          </div>
        </div>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped>
.wallpaper-item {
  transition: all 0.3s ease;
}

.wallpaper-item:hover {
  transform: translateY(-2px);
}
</style>
