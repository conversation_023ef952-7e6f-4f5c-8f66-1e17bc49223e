<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue';
import { 
  NCard, NSpace, NButton, NInput, NGrid, NGi, NImage,
  NTag, NModal, NForm, NFormItem, NSelect, NDatePicker,
  NSwitch, useMessage, useDialog, NEmpty, NUpload,
  NInputGroup, NIcon, NText, NDescriptions, NDescriptionsItem,
  NBadge, NStatistic, NInputNumber
} from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';

const { SvgIconVNode } = useSvgIcon();
const message = useMessage();
const dialog = useDialog();

interface Collection {
  id: number;
  title: string;
  description: string;
  cover: string;
  wallpaperCount: number;
  viewCount: number;
  downloadCount: number;
  status: 'online' | 'offline' | 'draft';
  featured: boolean;
  sort: number;
  createTime: string;
  updateTime: string;
}

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: null as string | null
});

// 专题列表
const collectionList = ref<Collection[]>([
  {
    id: 1,
    title: '春节特辑',
    description: '精选春节主题壁纸，包含传统元素和现代设计',
    cover: 'https://picsum.photos/400/250?random=11',
    wallpaperCount: 128,
    viewCount: 58900,
    downloadCount: 12300,
    status: 'online',
    featured: true,
    sort: 1,
    createTime: '2024-01-01',
    updateTime: '2024-01-20'
  },
  {
    id: 2,
    title: '极简主义',
    description: '简约不简单，极简风格壁纸合集',
    cover: 'https://picsum.photos/400/250?random=12',
    wallpaperCount: 86,
    viewCount: 42100,
    downloadCount: 8900,
    status: 'online',
    featured: true,
    sort: 2,
    createTime: '2024-01-05',
    updateTime: '2024-01-18'
  },
  {
    id: 3,
    title: '赛博朋克',
    description: '未来科技感十足的赛博朋克风格壁纸',
    cover: 'https://picsum.photos/400/250?random=13',
    wallpaperCount: 64,
    viewCount: 35600,
    downloadCount: 7200,
    status: 'online',
    featured: false,
    sort: 3,
    createTime: '2024-01-08',
    updateTime: '2024-01-15'
  },
  {
    id: 4,
    title: '自然风光',
    description: '大自然的鬼旧神工，绝美风景壁纸',
    cover: 'https://picsum.photos/400/250?random=14',
    wallpaperCount: 156,
    viewCount: 68900,
    downloadCount: 15600,
    status: 'online',
    featured: true,
    sort: 4,
    createTime: '2024-01-10',
    updateTime: '2024-01-12'
  },
  {
    id: 5,
    title: '动漫精选',
    description: '热门动漫作品壁纸精选集',
    cover: 'https://picsum.photos/400/250?random=15',
    wallpaperCount: 98,
    viewCount: 45200,
    downloadCount: 9800,
    status: 'offline',
    featured: false,
    sort: 5,
    createTime: '2024-01-12',
    updateTime: '2024-01-14'
  },
  {
    id: 6,
    title: '情人节特辑',
    description: '浪漫情人节主题壁纸',
    cover: 'https://picsum.photos/400/250?random=16',
    wallpaperCount: 42,
    viewCount: 12300,
    downloadCount: 3200,
    status: 'draft',
    featured: false,
    sort: 6,
    createTime: '2024-01-15',
    updateTime: '2024-01-16'
  }
]);

// 过滤后的专题列表
const filteredCollectionList = computed(() => {
  let result = collectionList.value;
  
  if (searchForm.keyword) {
    result = result.filter(item => 
      item.title.toLowerCase().includes(searchForm.keyword.toLowerCase()) ||
      item.description.toLowerCase().includes(searchForm.keyword.toLowerCase())
    );
  }
  
  if (searchForm.status) {
    result = result.filter(item => item.status === searchForm.status);
  }
  
  return result;
});

// 统计数据
const statistics = computed(() => {
  const total = collectionList.value.length;
  const online = collectionList.value.filter(c => c.status === 'online').length;
  const featured = collectionList.value.filter(c => c.featured).length;
  const totalWallpapers = collectionList.value.reduce((sum, c) => sum + c.wallpaperCount, 0);
  return { total, online, featured, totalWallpapers };
});

// 状态选项
const statusOptions = [
  { label: '全部', value: null },
  { label: '已上线', value: 'online' },
  { label: '已下线', value: 'offline' },
  { label: '草稿', value: 'draft' }
];

// 编辑模态框
const editModalVisible = ref(false);
const editForm = ref<Partial<Collection>>({});
const isEdit = ref(false);

// 管理壁纸模态框
const manageModalVisible = ref(false);
const currentCollection = ref<Collection | null>(null);

// 获取状态标签类型
const getStatusType = (status: string) => {
  const map: Record<string, any> = {
    online: 'success',
    offline: 'default',
    draft: 'warning'
  };
  return map[status] || 'default';
};

// 获取状态文本
const getStatusText = (status: string) => {
  const map: Record<string, string> = {
    online: '已上线',
    offline: '已下线',
    draft: '草稿'
  };
  return map[status] || '未知';
};

// 新增专题
const handleAdd = () => {
  isEdit.value = false;
  editForm.value = {
    title: '',
    description: '',
    cover: '',
    status: 'draft',
    featured: false,
    sort: 999
  };
  editModalVisible.value = true;
};

// 编辑专题
const handleEdit = (collection: Collection) => {
  isEdit.value = true;
  editForm.value = { ...collection };
  editModalVisible.value = true;
};

// 删除专题
const handleDelete = (collection: Collection) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除专题「${collection.title}」吗？`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = collectionList.value.findIndex(c => c.id === collection.id);
      if (index > -1) {
        collectionList.value.splice(index, 1);
        message.success('删除成功');
      }
    }
  });
};

// 管理壁纸
const handleManageWallpapers = (collection: Collection) => {
  currentCollection.value = collection;
  manageModalVisible.value = true;
};

// 切换状态
const handleToggleStatus = (collection: Collection) => {
  const newStatus = collection.status === 'online' ? 'offline' : 'online';
  const statusText = newStatus === 'online' ? '上线' : '下线';
  
  dialog.warning({
    title: '确认操作',
    content: `确定要${statusText}专题「${collection.title}」吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      collection.status = newStatus;
      message.success(`已${statusText}`);
    }
  });
};

// 切换推荐
const handleToggleFeatured = (collection: Collection) => {
  collection.featured = !collection.featured;
  message.success(collection.featured ? '已设为推荐' : '已取消推荐');
};

// 保存
const handleSave = () => {
  if (!editForm.value.title) {
    message.error('请输入专题标题');
    return;
  }
  
  if (isEdit.value) {
    // 编辑
    const index = collectionList.value.findIndex(c => c.id === editForm.value.id);
    if (index > -1) {
      collectionList.value[index] = {
        ...collectionList.value[index],
        ...editForm.value,
        updateTime: new Date().toISOString().split('T')[0]
      } as Collection;
      message.success('更新成功');
    }
  } else {
    // 新增
    const newCollection: Collection = {
      id: Math.max(...collectionList.value.map(c => c.id)) + 1,
      title: editForm.value.title!,
      description: editForm.value.description || '',
      cover: editForm.value.cover || 'https://picsum.photos/400/250?random=' + Date.now(),
      wallpaperCount: 0,
      viewCount: 0,
      downloadCount: 0,
      status: editForm.value.status as any || 'draft',
      featured: editForm.value.featured || false,
      sort: editForm.value.sort || 999,
      createTime: new Date().toISOString().split('T')[0],
      updateTime: new Date().toISOString().split('T')[0]
    };
    collectionList.value.push(newCollection);
    message.success('添加成功');
  }
  
  editModalVisible.value = false;
};

// 搜索
const handleSearch = () => {
  message.info('搜索完成');
};

// 重置
const handleReset = () => {
  searchForm.keyword = '';
  searchForm.status = null;
};
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 统计卡片 -->
    <NCard :bordered="false">
      <NGrid :x-gap="16" :cols="4" responsive="screen" item-responsive>
        <NGi>
          <NStatistic label="专题总数" :value="statistics.total">
            <template #prefix>
              <component :is="SvgIconVNode({ icon: 'material-symbols:collections-bookmark', fontSize: 20 })" />
            </template>
          </NStatistic>
        </NGi>
        <NGi>
          <NStatistic label="已上线" :value="statistics.online">
            <template #prefix>
              <component :is="SvgIconVNode({ icon: 'material-symbols:check-circle', fontSize: 20 })" />
            </template>
          </NStatistic>
        </NGi>
        <NGi>
          <NStatistic label="推荐专题" :value="statistics.featured">
            <template #prefix>
              <component :is="SvgIconVNode({ icon: 'material-symbols:star', fontSize: 20 })" />
            </template>
          </NStatistic>
        </NGi>
        <NGi>
          <NStatistic label="壁纸总数" :value="statistics.totalWallpapers">
            <template #prefix>
              <component :is="SvgIconVNode({ icon: 'material-symbols:image', fontSize: 20 })" />
            </template>
          </NStatistic>
        </NGi>
      </NGrid>
    </NCard>
    
    <!-- 搜索区域 -->
    <NCard :bordered="false">
      <NSpace>
        <NInput
          v-model:value="searchForm.keyword"
          placeholder="搜索专题标题或描述"
          clearable
          style="width: 300px"
        >
          <template #prefix>
            <component :is="SvgIconVNode({ icon: 'material-symbols:search' })" />
          </template>
        </NInput>
        <NSelect
          v-model:value="searchForm.status"
          :options="statusOptions"
          placeholder="选择状态"
          style="width: 150px"
        />
        <NButton type="primary" @click="handleSearch">搜索</NButton>
        <NButton @click="handleReset">重置</NButton>
        <NButton type="success" @click="handleAdd">
          <template #icon>
            <component :is="SvgIconVNode({ icon: 'material-symbols:add' })" />
          </template>
          新增专题
        </NButton>
      </NSpace>
    </NCard>
    
    <!-- 专题列表 -->
    <NGrid :x-gap="16" :y-gap="16" :cols="3" responsive="screen" item-responsive>
      <NGi v-for="collection in filteredCollectionList" :key="collection.id" span="3 s:3 m:1">
        <NCard :bordered="false" class="collection-card">
          <div class="relative">
            <!-- 封面图 -->
            <div class="relative overflow-hidden rounded-8px mb-12px">
              <NImage
                :src="collection.cover"
                :alt="collection.title"
                class="w-full h-180px object-cover"
                lazy
              />
              <!-- 状态标签 -->
              <div class="absolute top-8px right-8px flex gap-4px">
                <NTag :type="getStatusType(collection.status)" size="small">
                  {{ getStatusText(collection.status) }}
                </NTag>
                <NTag v-if="collection.featured" type="warning" size="small">
                  推荐
                </NTag>
              </div>
              <!-- 壁纸数量 -->
              <div class="absolute bottom-8px left-8px">
                <NTag type="info" size="small">
                  {{ collection.wallpaperCount }} 张壁纸
                </NTag>
              </div>
            </div>
            
            <!-- 信息区域 -->
            <div class="space-y-8px">
              <div class="font-bold text-16px">{{ collection.title }}</div>
              <div class="text-gray-500 text-12px line-clamp-2">{{ collection.description }}</div>
              
              <!-- 统计信息 -->
              <div class="flex items-center justify-between text-12px text-gray-500">
                <NSpace size="small">
                  <span class="flex items-center">
                    <component :is="SvgIconVNode({ icon: 'material-symbols:visibility', fontSize: 14 })" class="mr-2px" />
                    {{ collection.viewCount }}
                  </span>
                  <span class="flex items-center">
                    <component :is="SvgIconVNode({ icon: 'material-symbols:download', fontSize: 14 })" class="mr-2px" />
                    {{ collection.downloadCount }}
                  </span>
                </NSpace>
                <span>{{ collection.updateTime }}</span>
              </div>
              
              <!-- 操作按钮 -->
              <NSpace>
                <NButton size="small" @click="handleManageWallpapers(collection)">
                  管理壁纸
                </NButton>
                <NButton size="small" @click="handleEdit(collection)">
                  编辑
                </NButton>
                <NButton
                  size="small"
                  :type="collection.status === 'online' ? 'warning' : 'success'"
                  @click="handleToggleStatus(collection)"
                >
                  {{ collection.status === 'online' ? '下线' : '上线' }}
                </NButton>
                <NButton
                  size="small"
                  :type="collection.featured ? 'default' : 'warning'"
                  @click="handleToggleFeatured(collection)"
                >
                  {{ collection.featured ? '取消推荐' : '设为推荐' }}
                </NButton>
                <NButton size="small" type="error" @click="handleDelete(collection)">
                  删除
                </NButton>
              </NSpace>
            </div>
          </div>
        </NCard>
      </NGi>
    </NGrid>
    
    <!-- 编辑模态框 -->
    <NModal
      v-model:show="editModalVisible"
      :title="isEdit ? '编辑专题' : '新增专题'"
      preset="card"
      style="width: 600px"
    >
      <NForm :model="editForm" label-placement="left" label-width="80px">
        <NFormItem label="专题标题" required>
          <NInput v-model:value="editForm.title" placeholder="请输入专题标题" />
        </NFormItem>
        <NFormItem label="专题描述">
          <NInput
            v-model:value="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入专题描述"
          />
        </NFormItem>
        <NFormItem label="封面图片">
          <NInput v-model:value="editForm.cover" placeholder="请输入封面图片URL" />
        </NFormItem>
        <NFormItem label="状态">
          <NSelect
            v-model:value="editForm.status"
            :options="[
              { label: '草稿', value: 'draft' },
              { label: '已上线', value: 'online' },
              { label: '已下线', value: 'offline' }
            ]"
          />
        </NFormItem>
        <NFormItem label="推荐专题">
          <NSwitch v-model:value="editForm.featured" />
        </NFormItem>
        <NFormItem label="排序">
          <NInputNumber v-model:value="editForm.sort" :min="0" placeholder="数字越小越靠前" />
        </NFormItem>
      </NForm>
      
      <template #footer>
        <NSpace justify="end">
          <NButton @click="editModalVisible = false">取消</NButton>
          <NButton type="primary" @click="handleSave">保存</NButton>
        </NSpace>
      </template>
    </NModal>
    
    <!-- 管理壁纸模态框 -->
    <NModal
      v-model:show="manageModalVisible"
      :title="`管理专题壁纸 - ${currentCollection?.title}`"
      preset="card"
      style="width: 800px; height: 600px"
    >
      <div class="text-center py-40px text-gray-500">
        壁纸管理功能开发中...
      </div>
    </NModal>
  </NSpace>
</template>

<style scoped>
.collection-card {
  transition: all 0.3s;
}

.collection-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
