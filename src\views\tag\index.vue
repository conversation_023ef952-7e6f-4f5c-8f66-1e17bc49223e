<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue';
import { 
  NCard, NSpace, NButton, NInput, NTag, NGrid, NGi,
  NDataTable, NModal, NForm, NFormItem, NColorPicker,
  NInputNumber, useMessage, useDialog, NStatistic,
  NProgress, NEmpty, NDivider
} from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';

const { SvgIconVNode } = useSvgIcon();
const message = useMessage();
const dialog = useDialog();

interface Tag {
  id: number;
  name: string;
  color: string;
  count: number;
  hot: boolean;
  createTime: string;
  updateTime: string;
}

// 搜索关键词
const searchKeyword = ref('');

// 标签列表
const tagList = ref<Tag[]>([
  { id: 1, name: '4K', color: '#18a058', count: 5680, hot: true, createTime: '2024-01-01', updateTime: '2024-01-15' },
  { id: 2, name: '风景', color: '#2080f0', count: 4320, hot: true, createTime: '2024-01-02', updateTime: '2024-01-14' },
  { id: 3, name: '动漫', color: '#f0a020', count: 3890, hot: true, createTime: '2024-01-03', updateTime: '2024-01-13' },
  { id: 4, name: '美女', color: '#d03050', count: 3560, hot: true, createTime: '2024-01-04', updateTime: '2024-01-12' },
  { id: 5, name: '游戏', color: '#722ed1', count: 3210, hot: true, createTime: '2024-01-05', updateTime: '2024-01-11' },
  { id: 6, name: '暗黑', color: '#52c41a', count: 2980, hot: false, createTime: '2024-01-06', updateTime: '2024-01-10' },
  { id: 7, name: '简约', color: '#13c2c2', count: 2650, hot: false, createTime: '2024-01-07', updateTime: '2024-01-09' },
  { id: 8, name: '科技', color: '#eb2f96', count: 2320, hot: false, createTime: '2024-01-08', updateTime: '2024-01-08' },
  { id: 9, name: '城市', color: '#fa8c16', count: 1990, hot: false, createTime: '2024-01-09', updateTime: '2024-01-07' },
  { id: 10, name: '自然', color: '#a0d911', count: 1660, hot: false, createTime: '2024-01-10', updateTime: '2024-01-06' }
]);

// 过滤后的标签列表
const filteredTagList = computed(() => {
  if (!searchKeyword.value) return tagList.value;
  return tagList.value.filter(tag => 
    tag.name.toLowerCase().includes(searchKeyword.value.toLowerCase())
  );
});

// 热门标签
const hotTags = computed(() => {
  return tagList.value
    .filter(tag => tag.hot)
    .sort((a, b) => b.count - a.count)
    .slice(0, 10);
});

// 标签统计
const tagStatistics = computed(() => {
  const total = tagList.value.length;
  const totalUsage = tagList.value.reduce((sum, tag) => sum + tag.count, 0);
  const hotCount = tagList.value.filter(tag => tag.hot).length;
  return { total, totalUsage, hotCount };
});

// 编辑模态框
const editModalVisible = ref(false);
const editForm = ref<Partial<Tag>>({
  name: '',
  color: '#2080f0',
  hot: false
});
const isEdit = ref(false);

// 表格列定义
const columns: DataTableColumns<Tag> = [
  {
    title: 'ID',
    key: 'id',
    width: 80,
    align: 'center'
  },
  {
    title: '标签名称',
    key: 'name',
    render(row) {
      return h(NTag, {
        color: { color: row.color + '20', borderColor: row.color, textColor: row.color },
        size: 'medium'
      }, { default: () => row.name });
    }
  },
  {
    title: '颜色',
    key: 'color',
    width: 100,
    render(row) {
      return h('div', {
        class: 'flex items-center gap-8px'
      }, [
        h('div', {
          style: {
            width: '24px',
            height: '24px',
            borderRadius: '4px',
            backgroundColor: row.color
          }
        }),
        h('span', { class: 'text-12px text-gray-500' }, row.color)
      ]);
    }
  },
  {
    title: '使用次数',
    key: 'count',
    width: 120,
    sorter: (a, b) => a.count - b.count,
    render(row) {
      return h('span', { class: 'font-medium' }, row.count.toLocaleString());
    }
  },
  {
    title: '热门标签',
    key: 'hot',
    width: 100,
    align: 'center',
    render(row) {
      return h(NTag, {
        type: row.hot ? 'error' : 'default',
        size: 'small'
      }, { default: () => row.hot ? 'HOT' : '普通' });
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 120
  },
  {
    title: '更新时间',
    key: 'updateTime',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    render(row) {
      return h(NSpace, {}, {
        default: () => [
          h(NButton, {
            size: 'small',
            onClick: () => handleEdit(row)
          }, { default: () => '编辑' }),
          h(NButton, {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row)
          }, { default: () => '删除' })
        ]
      });
    }
  }
];

// 新增标签
const handleAdd = () => {
  isEdit.value = false;
  editForm.value = {
    name: '',
    color: '#2080f0',
    hot: false
  };
  editModalVisible.value = true;
};

// 编辑标签
const handleEdit = (tag: Tag) => {
  isEdit.value = true;
  editForm.value = { ...tag };
  editModalVisible.value = true;
};

// 删除标签
const handleDelete = (tag: Tag) => {
  dialog.warning({
    title: '确认删除',
    content: `确定要删除标签「${tag.name}」吗？`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      const index = tagList.value.findIndex(t => t.id === tag.id);
      if (index > -1) {
        tagList.value.splice(index, 1);
        message.success('删除成功');
      }
    }
  });
};

// 保存标签
const handleSave = () => {
  if (!editForm.value.name) {
    message.error('请输入标签名称');
    return;
  }
  
  if (isEdit.value) {
    // 编辑
    const index = tagList.value.findIndex(t => t.id === editForm.value.id);
    if (index > -1) {
      tagList.value[index] = {
        ...tagList.value[index],
        ...editForm.value,
        updateTime: new Date().toISOString().split('T')[0]
      } as Tag;
      message.success('更新成功');
    }
  } else {
    // 新增
    const newTag: Tag = {
      id: Math.max(...tagList.value.map(t => t.id)) + 1,
      name: editForm.value.name!,
      color: editForm.value.color || '#2080f0',
      count: 0,
      hot: editForm.value.hot || false,
      createTime: new Date().toISOString().split('T')[0],
      updateTime: new Date().toISOString().split('T')[0]
    };
    tagList.value.unshift(newTag);
    message.success('添加成功');
  }
  
  editModalVisible.value = false;
};

// 合并标签
const handleMerge = () => {
  message.info('合并标签功能开发中...');
};

// 刷新
const handleRefresh = () => {
  message.success('数据已刷新');
};
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 统计卡片 -->
    <NCard :bordered="false">
      <NGrid :x-gap="16" :cols="3">
        <NGi>
          <NStatistic label="标签总数" :value="tagStatistics.total">
            <template #prefix>
              <component :is="SvgIconVNode({ icon: 'material-symbols:label', fontSize: 20 })" />
            </template>
          </NStatistic>
        </NGi>
        <NGi>
          <NStatistic label="总使用次数" :value="tagStatistics.totalUsage">
            <template #prefix>
              <component :is="SvgIconVNode({ icon: 'material-symbols:trending-up', fontSize: 20 })" />
            </template>
          </NStatistic>
        </NGi>
        <NGi>
          <NStatistic label="热门标签" :value="tagStatistics.hotCount">
            <template #prefix>
              <component :is="SvgIconVNode({ icon: 'material-symbols:local-fire-department', fontSize: 20 })" />
            </template>
          </NStatistic>
        </NGi>
      </NGrid>
    </NCard>
    
    <!-- 热门标签云 -->
    <NCard :bordered="false" title="热门标签">
      <div class="flex flex-wrap gap-12px">
        <NTag
          v-for="tag in hotTags"
          :key="tag.id"
          :color="{ color: tag.color + '20', borderColor: tag.color, textColor: tag.color }"
          size="large"
          class="cursor-pointer hover:opacity-80 transition-opacity"
        >
          {{ tag.name }}
          <template #icon>
            <component :is="SvgIconVNode({ icon: 'material-symbols:local-fire-department', fontSize: 14 })" />
          </template>
        </NTag>
      </div>
    </NCard>
    
    <!-- 标签列表 -->
    <NCard :bordered="false" title="标签列表">
      <template #header-extra>
        <NSpace>
          <NInput
            v-model:value="searchKeyword"
            placeholder="搜索标签"
            clearable
            style="width: 200px"
          >
            <template #prefix>
              <component :is="SvgIconVNode({ icon: 'material-symbols:search' })" />
            </template>
          </NInput>
          <NButton type="primary" @click="handleAdd">
            <template #icon>
              <component :is="SvgIconVNode({ icon: 'material-symbols:add' })" />
            </template>
            新增标签
          </NButton>
          <NButton @click="handleMerge">
            <template #icon>
              <component :is="SvgIconVNode({ icon: 'material-symbols:merge' })" />
            </template>
            合并标签
          </NButton>
          <NButton @click="handleRefresh">
            <template #icon>
              <component :is="SvgIconVNode({ icon: 'material-symbols:refresh' })" />
            </template>
            刷新
          </NButton>
        </NSpace>
      </template>
      
      <NDataTable
        :columns="columns"
        :data="filteredTagList"
        :pagination="{
          pageSize: 10
        }"
        :bordered="false"
        :single-line="false"
        striped
      />
    </NCard>
    
    <!-- 编辑模态框 -->
    <NModal
      v-model:show="editModalVisible"
      :title="isEdit ? '编辑标签' : '新增标签'"
      preset="card"
      style="width: 500px"
    >
      <NForm :model="editForm" label-placement="left" label-width="80px">
        <NFormItem label="标签名称" required>
          <NInput v-model:value="editForm.name" placeholder="请输入标签名称" />
        </NFormItem>
        <NFormItem label="标签颜色">
          <NColorPicker v-model:value="editForm.color" :swatches="[
            '#18a058', '#2080f0', '#f0a020', '#d03050', '#722ed1',
            '#52c41a', '#13c2c2', '#eb2f96', '#fa8c16', '#a0d911'
          ]" />
        </NFormItem>
        <NFormItem label="热门标签">
          <NSpace>
            <NButton
              :type="editForm.hot ? 'error' : 'default'"
              @click="editForm.hot = !editForm.hot"
            >
              {{ editForm.hot ? '是' : '否' }}
            </NButton>
          </NSpace>
        </NFormItem>
      </NForm>
      
      <template #footer>
        <NSpace justify="end">
          <NButton @click="editModalVisible = false">取消</NButton>
          <NButton type="primary" @click="handleSave">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </NSpace>
</template>

<style scoped>
.tag-cloud-item {
  transition: all 0.3s ease;
}

.tag-cloud-item:hover {
  transform: scale(1.1);
}
</style>
