<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import type { ECOption } from '@/components/common/echarts-card.vue';

const chartRef = ref<HTMLDivElement>();
let chartInstance: echarts.ECharts | null = null;

const option: ECOption = {
  tooltip: {
    trigger: 'item',
    formatter: '{a} <br/>{b}: {c} ({d}%)'
  },
  legend: {
    orient: 'vertical',
    left: 'left',
    data: ['风景', '动漫', '美女', '游戏', '汽车', '影视', '动物', '其他']
  },
  series: [
    {
      name: '分类分布',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 20,
          fontWeight: 'bold'
        }
      },
      labelLine: {
        show: false
      },
      data: [
        { value: 3548, name: '风景', itemStyle: { color: '#5470c6' } },
        { value: 2735, name: '动漫', itemStyle: { color: '#91cc75' } },
        { value: 2580, name: '美女', itemStyle: { color: '#fac858' } },
        { value: 1484, name: '游戏', itemStyle: { color: '#ee6666' } },
        { value: 1300, name: '汽车', itemStyle: { color: '#73c0de' } },
        { value: 1200, name: '影视', itemStyle: { color: '#3ba272' } },
        { value: 800, name: '动物', itemStyle: { color: '#fc8452' } },
        { value: 600, name: '其他', itemStyle: { color: '#9a60b4' } }
      ]
    }
  ]
};

const initChart = () => {
  if (chartRef.value) {
    chartInstance = echarts.init(chartRef.value);
    chartInstance.setOption(option);
  }
};

const resizeChart = () => {
  chartInstance?.resize();
};

onMounted(() => {
  initChart();
  window.addEventListener('resize', resizeChart);
});

onUnmounted(() => {
  window.removeEventListener('resize', resizeChart);
  chartInstance?.dispose();
});
</script>

<template>
  <div ref="chartRef" class="w-full h-360px"></div>
</template>
