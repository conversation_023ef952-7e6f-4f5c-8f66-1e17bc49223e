<script setup lang="ts">
import { ref, reactive } from 'vue';
import { 
  NCard, NSpace, NButton, NUpload, NUploadDragger, NIcon, NText, 
  NForm, NFormItem, NInput, NSelect, NTag, NGrid, NGi,
  useMessage, NImage, NProgress, NAlert
} from 'naive-ui';
import type { UploadFileInfo } from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';

const { SvgIconVNode } = useSvgIcon();
const message = useMessage();

// 上传表单
const uploadForm = reactive({
  title: '',
  description: '',
  category: null,
  tags: [] as string[],
  resolution: '',
  copyright: ''
});

// 分类选项
const categoryOptions = [
  { label: '风景', value: 'landscape' },
  { label: '动漫', value: 'anime' },
  { label: '美女', value: 'beauty' },
  { label: '游戏', value: 'game' },
  { label: '汽车', value: 'car' },
  { label: '影视', value: 'movie' },
  { label: '动物', value: 'animal' },
  { label: '科技', value: 'tech' },
  { label: '其他', value: 'other' }
];

// 标签选项
const tagOptions = ref([
  { label: '4K', value: '4K' },
  { label: '风景', value: '风景' },
  { label: '自然', value: '自然' },
  { label: '城市', value: '城市' },
  { label: '动漫', value: '动漫' },
  { label: '游戏', value: '游戏' },
  { label: '简约', value: '简约' },
  { label: '暗黑', value: '暗黑' }
]);

// 文件列表
const fileList = ref<UploadFileInfo[]>([]);

// 上传进度
const uploadProgress = ref(0);
const isUploading = ref(false);

// 处理文件变化
const handleFileChange = (options: { fileList: UploadFileInfo[] }) => {
  fileList.value = options.fileList;
};

// 自定义上传
const customRequest = ({
  file,
  onFinish,
  onError,
  onProgress
}: any) => {
  // 模拟上传进度
  let progress = 0;
  const timer = setInterval(() => {
    progress += 10;
    onProgress({ percent: progress });
    
    if (progress >= 100) {
      clearInterval(timer);
      onFinish();
      message.success(`${file.name} 上传成功`);
    }
  }, 200);
  
  return {
    abort() {
      clearInterval(timer);
    }
  };
};

// 批量上传
const handleBatchUpload = () => {
  if (fileList.value.length === 0) {
    message.warning('请先选择要上传的图片');
    return;
  }
  
  if (!uploadForm.category) {
    message.warning('请选择分类');
    return;
  }
  
  isUploading.value = true;
  uploadProgress.value = 0;
  
  // 模拟批量上传
  const timer = setInterval(() => {
    uploadProgress.value += 10;
    
    if (uploadProgress.value >= 100) {
      clearInterval(timer);
      isUploading.value = false;
      message.success(`成功上传 ${fileList.value.length} 张壁纸`);
      
      // 重置表单
      handleReset();
    }
  }, 300);
};

// 重置表单
const handleReset = () => {
  uploadForm.title = '';
  uploadForm.description = '';
  uploadForm.category = null;
  uploadForm.tags = [];
  uploadForm.resolution = '';
  uploadForm.copyright = '';
  fileList.value = [];
  uploadProgress.value = 0;
};

// 文件校验
const beforeUpload = (data: {
  file: UploadFileInfo;
  fileList: UploadFileInfo[];
}) => {
  const file = data.file.file;
  if (!file) return true;
  
  // 检查文件类型
  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('只能上传图片文件');
    return false;
  }
  
  // 检查文件大小（10MB）
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('图片大小不能超过 10MB');
    return false;
  }
  
  return true;
};

// 移除文件
const handleRemove = (options: { file: UploadFileInfo }) => {
  const index = fileList.value.findIndex(f => f.id === options.file.id);
  if (index > -1) {
    fileList.value.splice(index, 1);
  }
  return true;
};
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 页面标题 -->
    <NCard :bordered="false">
      <div class="flex items-center justify-between">
        <div>
          <div class="text-24px font-bold mb-4px">上传壁纸</div>
          <div class="text-gray-500">支持批量上传，单个文件不超过10MB</div>
        </div>
        <NSpace>
          <NButton @click="handleReset">重置</NButton>
          <NButton type="primary" :loading="isUploading" @click="handleBatchUpload">
            <template #icon>
              <component :is="SvgIconVNode({ icon: 'material-symbols:cloud-upload' })" />
            </template>
            开始上传
          </NButton>
        </NSpace>
      </div>
    </NCard>
    
    <!-- 上传区域 -->
    <NGrid :x-gap="16" :cols="2" responsive="screen" item-responsive>
      <!-- 左侧：上传组件 -->
      <NGi span="2 m:1">
        <NCard :bordered="false" title="选择图片">
          <NUpload
            v-model:file-list="fileList"
            multiple
            directory-dnd
            :custom-request="customRequest"
            :before-upload="beforeUpload"
            @remove="handleRemove"
            @change="handleFileChange"
            :max="20"
            list-type="image-card"
            accept="image/*"
          >
            <NUploadDragger v-if="fileList.length === 0" style="height: 300px;">
              <div style="margin-bottom: 12px">
                <NIcon size="48" :depth="3">
                  <component :is="SvgIconVNode({ icon: 'material-symbols:cloud-upload', fontSize: 48 })" />
                </NIcon>
              </div>
              <NText style="font-size: 16px">
                点击或拖拽文件到此区域上传
              </NText>
              <NText depth="3" style="margin-top: 8px">
                支持 jpg、png、gif、webp 等格式，单个文件不超过10MB
              </NText>
            </NUploadDragger>
          </NUpload>
          
          <!-- 上传进度 -->
          <div v-if="isUploading" class="mt-16px">
            <div class="flex items-center justify-between mb-8px">
              <span>上传进度</span>
              <span>{{ uploadProgress }}%</span>
            </div>
            <NProgress
              type="line"
              :percentage="uploadProgress"
              :indicator-placement="'inside'"
              processing
            />
          </div>
          
          <!-- 文件统计 -->
          <div v-if="fileList.length > 0" class="mt-16px">
            <NAlert type="info" :bordered="false">
              已选择 {{ fileList.length }} 个文件，总大小：
              {{ (fileList.reduce((sum, file) => sum + (file.file?.size || 0), 0) / 1024 / 1024).toFixed(2) }} MB
            </NAlert>
          </div>
        </NCard>
      </NGi>
      
      <!-- 右侧：配置表单 -->
      <NGi span="2 m:1">
        <NCard :bordered="false" title="壁纸信息">
          <NForm :model="uploadForm" label-placement="top">
            <NFormItem label="标题（选填）">
              <NInput 
                v-model:value="uploadForm.title" 
                placeholder="为这批壁纸设置统一标题"
              />
            </NFormItem>
            
            <NFormItem label="描述（选填）">
              <NInput
                v-model:value="uploadForm.description"
                type="textarea"
                :rows="3"
                placeholder="添加描述信息"
              />
            </NFormItem>
            
            <NFormItem label="分类" required>
              <NSelect
                v-model:value="uploadForm.category"
                :options="categoryOptions"
                placeholder="请选择分类"
              />
            </NFormItem>
            
            <NFormItem label="标签">
              <NSelect
                v-model:value="uploadForm.tags"
                multiple
                :options="tagOptions"
                placeholder="选择或输入标签"
                tag
              />
            </NFormItem>
            
            <NFormItem label="分辨率（选填）">
              <NInput
                v-model:value="uploadForm.resolution"
                placeholder="如：1920x1080"
              />
            </NFormItem>
            
            <NFormItem label="版权信息（选填）">
              <NInput
                v-model:value="uploadForm.copyright"
                placeholder="图片来源或版权说明"
              />
            </NFormItem>
          </NForm>
          
          <!-- 上传提示 -->
          <NAlert type="warning" :bordered="false" class="mt-16px">
            <div class="text-12px">
              <div>上传须知：</div>
              <div>1. 请确保上传的图片拥有合法版权</div>
              <div>2. 不要上传违法、暴力、色情等不良内容</div>
              <div>3. 建议上传高清图片（分辨率不低于1920x1080）</div>
              <div>4. 系统会自动生成不同尺寸的缩略图</div>
            </div>
          </NAlert>
        </NCard>
      </NGi>
    </NGrid>
  </NSpace>
</template>

<style scoped>
:deep(.n-upload-file-list--grid) {
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
}
</style>
