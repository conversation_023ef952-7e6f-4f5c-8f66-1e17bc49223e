<script setup lang="ts">
import { ref, reactive, h, computed } from 'vue';
import {
  NCard, NSpace, NButton, NDataTable, NTag, NModal, NForm, NFormItem,
  NInput, NSelect, NTree, useMessage, NIcon, NPopconfirm, NSwitch,
  NDescriptions, NDescriptionsItem, NGrid, NGridItem, NStatistic,
  NCheckbox, NCheckboxGroup, NRadioGroup, NRadio, NTabs, NTabPane,
  NTransfer, NAlert, NText, NPagination
} from 'naive-ui';
import type { DataTableColumns, TreeOption, TransferOption } from 'naive-ui';
import { generateMockPermissionData, generateMockRoleData, type Permission, type Role, PermissionType, PermissionStatus } from '@/service/api/permission';

const message = useMessage();

// 使用Mock数据
const permissionData = ref<Permission[]>(generateMockPermissionData());
const roleData = ref<Role[]>(generateMockRoleData());

// 当前选中的标签页
const activeTab = ref('roles');

// 角色相关状态
const showRoleModal = ref(false);
const isEditRole = ref(false);
const currentRole = ref<Role | null>(null);

// 权限相关状态
const showPermissionModal = ref(false);
const isEditPermission = ref(false);
const currentPermission = ref<Permission | null>(null);

// 权限分配相关状态
const showAssignModal = ref(false);
const assignRoleId = ref('');
const selectedPermissions = ref<string[]>([]);

// 角色表单
const roleForm = ref({
  id: '',
  name: '',
  code: '',
  description: '',
  status: PermissionStatus.ENABLED,
  permissions: [] as string[]
});

// 权限表单
const permissionForm = ref({
  id: '',
  name: '',
  code: '',
  type: PermissionType.MENU,
  parentId: '',
  path: '',
  component: '',
  icon: '',
  sort: 0,
  status: PermissionStatus.ENABLED,
  description: ''
});

// 分页配置
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 0
});

// 搜索参数
const searchParams = reactive({
  name: '',
  status: null as PermissionStatus | null
});

// 权限类型选项
const permissionTypeOptions = [
  { label: '菜单', value: PermissionType.MENU },
  { label: '按钮', value: PermissionType.BUTTON },
  { label: 'API', value: PermissionType.API },
  { label: '数据', value: PermissionType.DATA }
];

// 状态选项
const statusOptions = [
  { label: '全部', value: null },
  { label: '启用', value: PermissionStatus.ENABLED },
  { label: '禁用', value: PermissionStatus.DISABLED }
];

// 转换权限数据为树形结构
const permissionTree = computed<TreeOption[]>(() => {
  const convertToTreeOption = (permissions: Permission[]): TreeOption[] => {
    return permissions.map(permission => ({
      key: permission.id,
      label: permission.name,
      children: permission.children ? convertToTreeOption(permission.children) : undefined,
      disabled: permission.status === PermissionStatus.DISABLED
    }));
  };
  return convertToTreeOption(permissionData.value);
});

// 转换权限数据为穿梭框选项
const permissionTransferOptions = computed<TransferOption[]>(() => {
  const flattenPermissions = (permissions: Permission[], result: TransferOption[] = []): TransferOption[] => {
    permissions.forEach(permission => {
      result.push({
        value: permission.id,
        label: permission.name,
        disabled: permission.status === PermissionStatus.DISABLED
      });
      if (permission.children) {
        flattenPermissions(permission.children, result);
      }
    });
    return result;
  };
  return flattenPermissions(permissionData.value);
});

// 角色表格列定义
const roleColumns: DataTableColumns = [
  {
    title: '角色名称',
    key: 'name',
    width: 150
  },
  {
    title: '角色编码',
    key: 'code',
    width: 150
  },
  {
    title: '描述',
    key: 'description',
    width: 200,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row: Role) {
      return h(NTag, {
        type: row.status === PermissionStatus.ENABLED ? 'success' : 'error'
      }, {
        default: () => row.status === PermissionStatus.ENABLED ? '启用' : '禁用'
      });
    }
  },
  {
    title: '用户数量',
    key: 'userCount',
    width: 100,
    render(row: Role) {
      return h(NText, {
        type: 'info'
      }, {
        default: () => `${row.userCount} 人`
      });
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render(row: Role) {
      return h(NSpace, null, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleEditRole(row)
          }, { default: () => '编辑' }),
          h(NButton, {
            size: 'small',
            type: 'info',
            ghost: true,
            onClick: () => handleAssignPermissions(row)
          }, { default: () => '分配权限' }),
          h(NPopconfirm, {
            onPositiveClick: () => handleDeleteRole(row.id)
          }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'error',
              ghost: true
            }, { default: () => '删除' }),
            default: () => '确定要删除该角色吗？'
          })
        ]
      });
    }
  }
];

// 过滤后的角色数据
const filteredRoleData = computed(() => {
  let data = [...roleData.value];

  if (searchParams.name) {
    data = data.filter(item =>
      item.name.includes(searchParams.name) ||
      item.code.includes(searchParams.name)
    );
  }

  if (searchParams.status !== null) {
    data = data.filter(item => item.status === searchParams.status);
  }

  pagination.total = data.length;

  const start = (pagination.page - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;

  return data.slice(start, end);
});

// 添加角色
const handleAddRole = () => {
  isEditRole.value = false;
  roleForm.value = {
    id: '',
    name: '',
    code: '',
    description: '',
    status: PermissionStatus.ENABLED,
    permissions: []
  };
  showRoleModal.value = true;
};

// 编辑角色
const handleEditRole = (role: Role) => {
  isEditRole.value = true;
  currentRole.value = role;
  roleForm.value = {
    id: role.id,
    name: role.name,
    code: role.code,
    description: role.description || '',
    status: role.status,
    permissions: [...role.permissions]
  };
  showRoleModal.value = true;
};

// 删除角色
const handleDeleteRole = (roleId: string) => {
  const index = roleData.value.findIndex(item => item.id === roleId);
  if (index > -1) {
    roleData.value.splice(index, 1);
    message.success('删除成功');
  }
};

// 分配权限
const handleAssignPermissions = (role: Role) => {
  assignRoleId.value = role.id;
  selectedPermissions.value = [...role.permissions];
  showAssignModal.value = true;
};

// 保存角色
const handleSaveRole = () => {
  if (isEditRole.value && currentRole.value) {
    const index = roleData.value.findIndex(item => item.id === currentRole.value!.id);
    if (index > -1) {
      roleData.value[index] = {
        ...roleData.value[index],
        name: roleForm.value.name,
        code: roleForm.value.code,
        description: roleForm.value.description,
        status: roleForm.value.status,
        permissions: [...roleForm.value.permissions],
        updateTime: new Date().toLocaleString()
      };
    }
    message.success('更新成功');
  } else {
    const newRole: Role = {
      id: Date.now().toString(),
      name: roleForm.value.name,
      code: roleForm.value.code,
      description: roleForm.value.description,
      status: roleForm.value.status,
      permissions: [...roleForm.value.permissions],
      userCount: 0,
      createTime: new Date().toLocaleString(),
      updateTime: new Date().toLocaleString()
    };
    roleData.value.unshift(newRole);
    message.success('添加成功');
  }
  showRoleModal.value = false;
};

// 保存权限分配
const handleSavePermissionAssign = () => {
  const roleIndex = roleData.value.findIndex(item => item.id === assignRoleId.value);
  if (roleIndex > -1) {
    roleData.value[roleIndex].permissions = [...selectedPermissions.value];
    roleData.value[roleIndex].updateTime = new Date().toLocaleString();
    message.success('权限分配成功');
  }
  showAssignModal.value = false;
};

// 搜索角色
const handleSearch = () => {
  pagination.page = 1;
};

// 重置搜索
const handleReset = () => {
  searchParams.name = '';
  searchParams.status = null;
  pagination.page = 1;
};




</script>

<template>
  <div class="space-y-4">
    <!-- 统计卡片 -->
    <NGrid :cols="4" :x-gap="16">
      <NGridItem>
        <NCard size="small">
          <NStatistic label="角色总数">
            <template #prefix>
              <SvgIcon icon="carbon:user-role" class="text-20px text-primary" />
            </template>
            {{ roleData.length }}
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="权限点数">
            <template #prefix>
              <SvgIcon icon="carbon:security" class="text-20px text-warning" />
            </template>
            42
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="菜单项数">
            <template #prefix>
              <SvgIcon icon="carbon:list" class="text-20px text-info" />
            </template>
            18
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="在线管理员">
            3
          </NStatistic>
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 主卡片 -->
    <NCard :bordered="false">
      <NTabs v-model:value="activeTab" type="line">
        <!-- 角色管理标签页 -->
        <NTabPane name="roles" tab="角色管理">
          <NSpace vertical :size="16">
            <!-- 搜索区域 -->
            <NSpace justify="space-between">
              <NSpace>
                <NInput
                  v-model:value="searchParams.name"
                  placeholder="搜索角色名称或编码"
                  clearable
                  style="width: 200px"
                >
                  <template #prefix>
                    <SvgIcon icon="carbon:search" />
                  </template>
                </NInput>
                <NSelect
                  v-model:value="searchParams.status"
                  :options="statusOptions"
                  placeholder="状态"
                  clearable
                  style="width: 120px"
                />
                <NButton type="primary" @click="handleSearch">
                  <template #icon>
                    <SvgIcon icon="carbon:search" />
                  </template>
                  搜索
                </NButton>
                <NButton @click="handleReset">重置</NButton>
              </NSpace>

              <NSpace>
                <NButton type="primary" @click="handleAddRole">
                  <template #icon>
                    <SvgIcon icon="carbon:add" />
                  </template>
                  添加角色
                </NButton>
              </NSpace>
            </NSpace>

            <!-- 角色表格 -->
            <NDataTable
              :columns="roleColumns"
              :data="filteredRoleData"
              :row-key="(row: any) => row.id"
              striped
            />

            <!-- 分页 -->
            <div class="flex justify-end">
              <NPagination
                v-model:page="pagination.page"
                v-model:page-size="pagination.pageSize"
                :item-count="pagination.total"
                :page-sizes="[10, 20, 30, 50]"
                show-size-picker
              />
            </div>
          </NSpace>
        </NTabPane>

        <!-- 权限配置标签页 -->
        <NTabPane name="permissions" tab="权限配置">
          <div class="py-4">
            <NCard title="功能权限树" :bordered="false" size="small">
              <NTree
                :data="permissionTree"
                checkable
                cascade
                block-line
                expand-on-click
                default-expand-all
              />
            </NCard>
          </div>
        </NTabPane>
      </NTabs>
    </NCard>

    <!-- 角色编辑弹窗 -->
    <NModal
      v-model:show="showRoleModal"
      :title="isEditRole ? '编辑角色' : '添加角色'"
      preset="dialog"
      style="width: 600px"
    >
      <NForm :model="roleForm" label-placement="left" label-width="100px">
        <NFormItem label="角色名称" path="name">
          <NInput v-model:value="roleForm.name" placeholder="请输入角色名称" />
        </NFormItem>
        <NFormItem label="角色编码" path="code">
          <NInput v-model:value="roleForm.code" placeholder="请输入角色编码" />
        </NFormItem>
        <NFormItem label="角色描述" path="description">
          <NInput
            v-model:value="roleForm.description"
            type="textarea"
            placeholder="请输入角色描述"
            :rows="3"
          />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NRadioGroup v-model:value="roleForm.status">
            <NRadio :value="PermissionStatus.ENABLED">启用</NRadio>
            <NRadio :value="PermissionStatus.DISABLED">禁用</NRadio>
          </NRadioGroup>
        </NFormItem>
      </NForm>
      <template #action>
        <NSpace>
          <NButton @click="showRoleModal = false">取消</NButton>
          <NButton type="primary" @click="handleSaveRole">保存</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 权限分配弹窗 -->
    <NModal
      v-model:show="showAssignModal"
      title="分配权限"
      preset="dialog"
      style="width: 800px"
    >
      <div class="space-y-4">
        <NAlert type="info" size="small">
          为角色分配权限，选中的权限将授予该角色下的所有用户。
        </NAlert>

        <NCard title="权限选择" size="small">
          <NTransfer
            v-model:value="selectedPermissions"
            :options="permissionTransferOptions"
            source-title="可选权限"
            target-title="已选权限"
            :source-filterable="true"
            :target-filterable="true"
          />
        </NCard>
      </div>
      <template #action>
        <NSpace>
          <NButton @click="showAssignModal = false">取消</NButton>
          <NButton type="primary" @click="handleSavePermissionAssign">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>
                block-line
                expand-on-click
                default-expand-all
              />
            </NCard>
          </div>
        </NTabPane>

        <!-- 菜单配置标签页 -->
        <NTabPane name="menus" tab="菜单配置">
          <div class="py-4">
            <NCard title="菜单权限树" :bordered="false" size="small">
              <NTree
                :data="menuTree"
                checkable
                cascade
                block-line
                expand-on-click
                default-expand-all
              />
            </NCard>
          </div>
        </NTabPane>
      </NTabs>
    </NCard>

    <!-- 角色表单弹窗 -->
    <NModal
      v-model:show="showRoleModal"
      :title="isEditRole ? '编辑角色' : '添加角色'"
      preset="dialog"
      style="width: 800px"
    >
      <NForm :model="roleForm" label-placement="left" label-width="100px">
        <NGrid :cols="2" :x-gap="16">
          <NGridItem>
            <NFormItem label="角色名称" path="name">
              <NInput v-model:value="roleForm.name" placeholder="请输入角色名称" />
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="角色编码" path="code">
              <NInput v-model:value="roleForm.code" placeholder="请输入角色编码" />
            </NFormItem>
          </NGridItem>
        </NGrid>
        <NFormItem label="角色描述" path="description">
          <NInput 
            v-model:value="roleForm.description" 
            type="textarea" 
            placeholder="请输入角色描述"
            :rows="3"
          />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NRadioGroup v-model:value="roleForm.status">
            <NRadio :value="1">启用</NRadio>
            <NRadio :value="0">禁用</NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <NTabs type="segment">
          <NTabPane name="permissions" tab="功能权限">
            <div class="h-300px overflow-y-auto">
              <NTree
                :data="permissionTree"
                checkable
                cascade
                block-line
                :checked-keys="checkedPermissions"
                @update:checked-keys="handlePermissionCheck"
              />
            </div>
          </NTabPane>
          <NTabPane name="menus" tab="菜单权限">
            <div class="h-300px overflow-y-auto">
              <NTree
                :data="menuTree"
                checkable
                cascade
                block-line
                :checked-keys="checkedMenus"
                @update:checked-keys="handleMenuCheck"
              />
            </div>
          </NTabPane>
        </NTabs>
      </NForm>
      <template #action>
        <NSpace justify="end">
          <NButton @click="showRoleModal = false">取消</NButton>
          <NButton type="primary" @click="handleSaveRole">保存</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 权限详情弹窗 -->
    <NModal
      v-model:show="showPermissionDetail"
      title="权限详情"
      preset="dialog"
      style="width: 700px"
    >
      <NDescriptions v-if="currentRole" :column="1" label-placement="left">
        <NDescriptionsItem label="角色名称">{{ currentRole.name }}</NDescriptionsItem>
        <NDescriptionsItem label="角色编码">
          <NTag type="info">{{ currentRole.code }}</NTag>
        </NDescriptionsItem>
        <NDescriptionsItem label="角色描述">{{ currentRole.description }}</NDescriptionsItem>
        <NDescriptionsItem label="用户数量">{{ currentRole.userCount }}</NDescriptionsItem>
        <NDescriptionsItem label="创建时间">{{ currentRole.createTime }}</NDescriptionsItem>
        <NDescriptionsItem label="权限列表">
          <NSpace>
            <NTag v-for="perm in currentRole.permissions" :key="perm" size="small">
              {{ perm }}
            </NTag>
          </NSpace>
        </NDescriptionsItem>
      </NDescriptions>
    </NModal>
  </div>
</template>
