<script setup lang="ts">
import { ref, reactive, h } from 'vue';
import { 
  NCard, NSpace, NButton, NDataTable, NTag, NModal, NForm, NFormItem, 
  NInput, NSelect, NTree, useMessage, NIcon, NPopconfirm, NSwitch,
  NDescriptions, NDescriptionsItem, NGrid, NGridItem, NStatistic,
  NCheckbox, NCheckboxGroup, NRadioGroup, NRadio, NTabs, NTabPane
} from 'naive-ui';
import type { DataTableColumns, TreeOption } from 'naive-ui';

const message = useMessage();

// 角色数据
const roleData = ref([
  {
    id: '1',
    name: '超级管理员',
    code: 'super_admin',
    description: '拥有系统所有权限',
    status: 1,
    userCount: 1,
    createTime: '2024-01-01 10:00:00',
    permissions: ['*']
  },
  {
    id: '2',
    name: '管理员',
    code: 'admin',
    description: '拥有部分系统管理权限',
    status: 1,
    userCount: 3,
    createTime: '2024-01-01 10:00:00',
    permissions: ['wallpaper:*', 'category:*', 'tag:*', 'banner:*', 'collection:*']
  },
  {
    id: '3',
    name: 'VIP用户',
    code: 'vip',
    description: 'VIP会员用户',
    status: 1,
    userCount: 15,
    createTime: '2024-01-15 14:00:00',
    permissions: ['wallpaper:view', 'wallpaper:download', 'collection:view']
  },
  {
    id: '4',
    name: '普通用户',
    code: 'user',
    description: '普通注册用户',
    status: 1,
    userCount: 156,
    createTime: '2024-01-01 10:00:00',
    permissions: ['wallpaper:view', 'collection:view']
  }
]);

// 权限树数据
const permissionTree: TreeOption[] = [
  {
    label: '壁纸管理',
    key: 'wallpaper',
    children: [
      { label: '查看壁纸', key: 'wallpaper:view' },
      { label: '上传壁纸', key: 'wallpaper:upload' },
      { label: '编辑壁纸', key: 'wallpaper:edit' },
      { label: '删除壁纸', key: 'wallpaper:delete' },
      { label: '下载壁纸', key: 'wallpaper:download' },
      { label: '审核壁纸', key: 'wallpaper:audit' }
    ]
  },
  {
    label: '分类管理',
    key: 'category',
    children: [
      { label: '查看分类', key: 'category:view' },
      { label: '添加分类', key: 'category:add' },
      { label: '编辑分类', key: 'category:edit' },
      { label: '删除分类', key: 'category:delete' }
    ]
  },
  {
    label: '标签管理',
    key: 'tag',
    children: [
      { label: '查看标签', key: 'tag:view' },
      { label: '添加标签', key: 'tag:add' },
      { label: '编辑标签', key: 'tag:edit' },
      { label: '删除标签', key: 'tag:delete' }
    ]
  },
  {
    label: '专题管理',
    key: 'collection',
    children: [
      { label: '查看专题', key: 'collection:view' },
      { label: '创建专题', key: 'collection:add' },
      { label: '编辑专题', key: 'collection:edit' },
      { label: '删除专题', key: 'collection:delete' }
    ]
  },
  {
    label: 'Banner管理',
    key: 'banner',
    children: [
      { label: '查看Banner', key: 'banner:view' },
      { label: '添加Banner', key: 'banner:add' },
      { label: '编辑Banner', key: 'banner:edit' },
      { label: '删除Banner', key: 'banner:delete' }
    ]
  },
  {
    label: '用户管理',
    key: 'user',
    children: [
      { label: '查看用户', key: 'user:view' },
      { label: '添加用户', key: 'user:add' },
      { label: '编辑用户', key: 'user:edit' },
      { label: '删除用户', key: 'user:delete' },
      { label: '封禁用户', key: 'user:ban' }
    ]
  },
  {
    label: '系统设置',
    key: 'settings',
    children: [
      { label: '查看设置', key: 'settings:view' },
      { label: '修改设置', key: 'settings:edit' },
      { label: 'API管理', key: 'settings:api' },
      { label: '存储管理', key: 'settings:storage' }
    ]
  },
  {
    label: '数据分析',
    key: 'analytics',
    children: [
      { label: '查看统计', key: 'analytics:view' },
      { label: '导出数据', key: 'analytics:export' }
    ]
  }
];

// 菜单权限数据
const menuTree: TreeOption[] = [
  {
    label: '首页',
    key: 'home',
    disabled: true
  },
  {
    label: '壁纸管理',
    key: 'wallpaper',
    children: [
      { label: '壁纸列表', key: 'wallpaper_list' },
      { label: '上传壁纸', key: 'wallpaper_upload' }
    ]
  },
  {
    label: '分类管理',
    key: 'category'
  },
  {
    label: '标签管理',
    key: 'tag'
  },
  {
    label: '专题管理',
    key: 'collection'
  },
  {
    label: 'Banner管理',
    key: 'banner'
  },
  {
    label: '用户管理',
    key: 'user',
    children: [
      { label: '用户列表', key: 'user_list' },
      { label: '权限管理', key: 'user_permission' }
    ]
  },
  {
    label: '数据分析',
    key: 'analytics',
    children: [
      { label: '下载统计', key: 'analytics_download' },
      { label: '搜索分析', key: 'analytics_search' },
      { label: '用户分析', key: 'analytics_user' }
    ]
  },
  {
    label: '系统设置',
    key: 'settings',
    children: [
      { label: 'API管理', key: 'settings_api' },
      { label: '基础配置', key: 'settings_basic' },
      { label: '存储管理', key: 'settings_storage' }
    ]
  }
];

// 当前选中的标签页
const activeTab = ref('roles');

// 角色表单
const showRoleModal = ref(false);
const isEditRole = ref(false);
const roleForm = ref({
  id: '',
  name: '',
  code: '',
  description: '',
  status: 1,
  permissions: [] as string[],
  menus: [] as string[]
});

// 选中的权限
const checkedPermissions = ref<string[]>([]);
const checkedMenus = ref<string[]>([]);

// 角色列配置
const roleColumns: DataTableColumns = [
  {
    type: 'selection',
    fixed: 'left'
  },
  {
    title: '角色名称',
    key: 'name',
    width: 150,
    render(row: any) {
      return h('div', { class: 'flex items-center gap-2' }, [
        h(SvgIcon, { icon: 'carbon:user-role', class: 'text-18px text-primary' }),
        h('span', { class: 'font-medium' }, row.name)
      ]);
    }
  },
  {
    title: '角色编码',
    key: 'code',
    width: 150,
    render(row: any) {
      return h(NTag, { type: 'info' }, { default: () => row.code });
    }
  },
  {
    title: '描述',
    key: 'description',
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '用户数',
    key: 'userCount',
    width: 100,
    render(row: any) {
      return h('span', { class: 'text-primary' }, row.userCount);
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row: any) {
      return h(NSwitch, {
        value: row.status === 1,
        onUpdateValue: (v: boolean) => handleToggleStatus(row, v)
      });
    }
  },
  {
    title: '创建时间',
    key: 'createTime',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render(row: any) {
      return h(NSpace, null, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleEditRole(row)
          }, { default: () => '配置权限' }),
          h(NButton, {
            size: 'small',
            ghost: true,
            onClick: () => handleViewPermissions(row)
          }, { default: () => '查看' }),
          h(NPopconfirm, {
            onPositiveClick: () => handleDeleteRole(row),
            disabled: row.code === 'super_admin'
          }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'error',
              ghost: true,
              disabled: row.code === 'super_admin'
            }, { default: () => '删除' }),
            default: () => '确定要删除该角色吗？'
          })
        ]
      });
    }
  }
];

// 查看权限详情
const showPermissionDetail = ref(false);
const currentRole = ref<any>(null);

// 处理状态切换
const handleToggleStatus = (row: any, value: boolean) => {
  row.status = value ? 1 : 0;
  message.success(`${value ? '启用' : '禁用'}成功`);
};

// 添加角色
const handleAddRole = () => {
  isEditRole.value = false;
  roleForm.value = {
    id: '',
    name: '',
    code: '',
    description: '',
    status: 1,
    permissions: [],
    menus: []
  };
  checkedPermissions.value = [];
  checkedMenus.value = [];
  showRoleModal.value = true;
};

// 编辑角色
const handleEditRole = (row: any) => {
  isEditRole.value = true;
  roleForm.value = { ...row };
  checkedPermissions.value = row.permissions || [];
  checkedMenus.value = row.menus || [];
  showRoleModal.value = true;
};

// 删除角色
const handleDeleteRole = (row: any) => {
  const index = roleData.value.findIndex(item => item.id === row.id);
  if (index > -1) {
    roleData.value.splice(index, 1);
    message.success('删除成功');
  }
};

// 查看权限
const handleViewPermissions = (row: any) => {
  currentRole.value = row;
  showPermissionDetail.value = true;
};

// 保存角色
const handleSaveRole = () => {
  roleForm.value.permissions = checkedPermissions.value;
  roleForm.value.menus = checkedMenus.value;
  
  if (isEditRole.value) {
    const index = roleData.value.findIndex(item => item.id === roleForm.value.id);
    if (index > -1) {
      roleData.value[index] = { ...roleData.value[index], ...roleForm.value };
    }
    message.success('更新成功');
  } else {
    const newRole = {
      ...roleForm.value,
      id: Date.now().toString(),
      createTime: new Date().toLocaleString(),
      userCount: 0
    };
    roleData.value.push(newRole);
    message.success('添加成功');
  }
  showRoleModal.value = false;
};

// 处理权限树选择
const handlePermissionCheck = (keys: string[]) => {
  checkedPermissions.value = keys;
};

// 处理菜单树选择
const handleMenuCheck = (keys: string[]) => {
  checkedMenus.value = keys;
};
</script>

<template>
  <div class="space-y-4">
    <!-- 统计卡片 -->
    <NGrid :cols="4" :x-gap="16">
      <NGridItem>
        <NCard size="small">
          <NStatistic label="角色总数">
            <template #prefix>
              <SvgIcon icon="carbon:user-role" class="text-20px text-primary" />
            </template>
            {{ roleData.length }}
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="权限点数">
            <template #prefix>
              <SvgIcon icon="carbon:security" class="text-20px text-warning" />
            </template>
            42
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="菜单项数">
            <template #prefix>
              <SvgIcon icon="carbon:list" class="text-20px text-info" />
            </template>
            18
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="在线管理员">
            3
          </NStatistic>
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 主卡片 -->
    <NCard :bordered="false">
      <NTabs v-model:value="activeTab" type="line">
        <!-- 角色管理标签页 -->
        <NTabPane name="roles" tab="角色管理">
          <NSpace vertical :size="16">
            <!-- 操作按钮 -->
            <NSpace justify="space-between">
              <NSpace>
                <NButton type="primary" @click="handleAddRole">
                  <template #icon>
                    <SvgIcon icon="carbon:add" />
                  </template>
                  添加角色
                </NButton>
              </NSpace>
            </NSpace>

            <!-- 角色表格 -->
            <NDataTable
              :columns="roleColumns"
              :data="roleData"
              :row-key="(row: any) => row.id"
              striped
            />
          </NSpace>
        </NTabPane>

        <!-- 权限配置标签页 -->
        <NTabPane name="permissions" tab="权限配置">
          <div class="py-4">
            <NCard title="功能权限树" :bordered="false" size="small">
              <NTree
                :data="permissionTree"
                checkable
                cascade
                block-line
                expand-on-click
                default-expand-all
              />
            </NCard>
          </div>
        </NTabPane>

        <!-- 菜单配置标签页 -->
        <NTabPane name="menus" tab="菜单配置">
          <div class="py-4">
            <NCard title="菜单权限树" :bordered="false" size="small">
              <NTree
                :data="menuTree"
                checkable
                cascade
                block-line
                expand-on-click
                default-expand-all
              />
            </NCard>
          </div>
        </NTabPane>
      </NTabs>
    </NCard>

    <!-- 角色表单弹窗 -->
    <NModal
      v-model:show="showRoleModal"
      :title="isEditRole ? '编辑角色' : '添加角色'"
      preset="dialog"
      style="width: 800px"
    >
      <NForm :model="roleForm" label-placement="left" label-width="100px">
        <NGrid :cols="2" :x-gap="16">
          <NGridItem>
            <NFormItem label="角色名称" path="name">
              <NInput v-model:value="roleForm.name" placeholder="请输入角色名称" />
            </NFormItem>
          </NGridItem>
          <NGridItem>
            <NFormItem label="角色编码" path="code">
              <NInput v-model:value="roleForm.code" placeholder="请输入角色编码" />
            </NFormItem>
          </NGridItem>
        </NGrid>
        <NFormItem label="角色描述" path="description">
          <NInput 
            v-model:value="roleForm.description" 
            type="textarea" 
            placeholder="请输入角色描述"
            :rows="3"
          />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NRadioGroup v-model:value="roleForm.status">
            <NRadio :value="1">启用</NRadio>
            <NRadio :value="0">禁用</NRadio>
          </NRadioGroup>
        </NFormItem>
        
        <NTabs type="segment">
          <NTabPane name="permissions" tab="功能权限">
            <div class="h-300px overflow-y-auto">
              <NTree
                :data="permissionTree"
                checkable
                cascade
                block-line
                :checked-keys="checkedPermissions"
                @update:checked-keys="handlePermissionCheck"
              />
            </div>
          </NTabPane>
          <NTabPane name="menus" tab="菜单权限">
            <div class="h-300px overflow-y-auto">
              <NTree
                :data="menuTree"
                checkable
                cascade
                block-line
                :checked-keys="checkedMenus"
                @update:checked-keys="handleMenuCheck"
              />
            </div>
          </NTabPane>
        </NTabs>
      </NForm>
      <template #action>
        <NSpace justify="end">
          <NButton @click="showRoleModal = false">取消</NButton>
          <NButton type="primary" @click="handleSaveRole">保存</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 权限详情弹窗 -->
    <NModal
      v-model:show="showPermissionDetail"
      title="权限详情"
      preset="dialog"
      style="width: 700px"
    >
      <NDescriptions v-if="currentRole" :column="1" label-placement="left">
        <NDescriptionsItem label="角色名称">{{ currentRole.name }}</NDescriptionsItem>
        <NDescriptionsItem label="角色编码">
          <NTag type="info">{{ currentRole.code }}</NTag>
        </NDescriptionsItem>
        <NDescriptionsItem label="角色描述">{{ currentRole.description }}</NDescriptionsItem>
        <NDescriptionsItem label="用户数量">{{ currentRole.userCount }}</NDescriptionsItem>
        <NDescriptionsItem label="创建时间">{{ currentRole.createTime }}</NDescriptionsItem>
        <NDescriptionsItem label="权限列表">
          <NSpace>
            <NTag v-for="perm in currentRole.permissions" :key="perm" size="small">
              {{ perm }}
            </NTag>
          </NSpace>
        </NDescriptionsItem>
      </NDescriptions>
    </NModal>
  </div>
</template>
