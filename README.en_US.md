<div align="center">
	<img src="./public/favicon.svg" width="160" />
	<h1>QingYun Wallpaper Admin System</h1>
  <span><a href="./README.md">中文</a> | English</span>
</div>

---

[![license](https://img.shields.io/badge/license-MIT-green.svg)](./LICENSE)
![vue](https://img.shields.io/badge/vue-3.5.20-brightgreen.svg)
![vite](https://img.shields.io/badge/vite-7.1.3-brightgreen.svg)
![typescript](https://img.shields.io/badge/typescript-5.9.2-blue.svg)
![naiveui](https://img.shields.io/badge/naiveui-2.42.0-blue.svg)

> [!NOTE]
> If you find QingYun Wallpaper Admin System helpful, or if you like our project, please give us a ⭐️ on GitHub. Your support is our motivation to keep improving and adding new features! Thank you for your support!

## 🌟 Introduction

**QingYun Wallpaper Admin System** is a professional wallpaper content management platform, specifically designed for wallpaper websites and applications. Built on the latest frontend technology stack including Vue3, Vite7, TypeScript, Pinia, and UnoCSS, it provides a complete wallpaper management solution.

The system adopts modern design concepts with a fresh and elegant interface that is powerful yet easy to use. Whether managing massive wallpaper resources, categorizing and tagging, or controlling user permissions, QingYun Wallpaper Admin System handles it all with ease.

## ✨ Features

### 🎨 Modern Design
- **Fresh and elegant interface**: Carefully designed UI components provide a comfortable visual experience
- **Dark mode support**: Built-in dark/light theme switching to protect your eyes
- **Responsive layout**: Perfect adaptation for all devices, from desktop to mobile

### 🚀 Technology Stack
- **Vue 3.5+**: Latest Vue 3 Composition API
- **Vite 7**: Lightning-fast development experience
- **TypeScript 5**: Type safety, improving code quality
- **Pinia**: Next-generation state management solution
- **UnoCSS**: Atomic CSS engine, efficient and convenient

### 📦 Functional Features
- **Wallpaper Management**: Support batch upload, category management, tagging system
- **User System**: Complete user permission management system
- **Data Statistics**: Intuitive data visualization display
- **Internationalization**: Built-in Chinese and English support, expandable to more languages
- **Theme Configuration**: Rich theme customization options
- **Route Permissions**: Flexible frontend and backend route permission control

### 🛠️ Development Experience
- **Code Standards**: Integrated ESLint and Prettier to ensure code quality
- **Automatic Routing**: File-based automatic route generation
- **Auto Import Components**: No need to manually import common components
- **Git Hooks**: Automatic formatting and type checking when committing code

## 📸 Preview

### Login Page
Clean and simple login interface with multiple login methods

### Dashboard
Data overview at a glance with real-time display of key metrics

### Wallpaper Management
Powerful wallpaper management features with batch operations support

### Theme Configuration
Rich theme configuration options to create a personalized interface

## 🚀 Quick Start

### Requirements

- **Node.js**: >= 20.19.0
- **pnpm**: >= 10.5.0

### Installation Steps

1. **Clone the project**
```bash
git clone https://github.com/qingyun-wallpaper/admin.git
cd admin
```

2. **Install dependencies**
```bash
pnpm install
```

3. **Start development server**
```bash
pnpm dev
```

4. **Build for production**
```bash
pnpm build
```

### Common Commands

```bash
# Start development environment
pnpm dev

# Build for production
pnpm build

# Preview build result
pnpm preview

# Code formatting
pnpm lint

# Type checking
pnpm typecheck

# Commit code (using conventional commits)
pnpm commit
```

## 📁 Project Structure

```
qingyun-wallpaper-admin/
├── .vscode/                # VSCode configuration
├── public/                 # Static assets
├── src/                    # Source code
│   ├── assets/            # Asset files
│   ├── components/        # Components
│   ├── composables/       # Composition functions
│   ├── constants/         # Constants
│   ├── hooks/             # Hook functions
│   ├── layouts/           # Layout components
│   ├── locales/           # Internationalization files
│   ├── router/            # Router configuration
│   ├── service/           # API services
│   ├── store/             # State management
│   ├── styles/            # Global styles
│   ├── typings/           # TypeScript type definitions
│   ├── utils/             # Utility functions
│   └── views/             # Page components
├── .env                    # Environment variables
├── .eslintrc.js           # ESLint configuration
├── index.html             # HTML template
├── package.json           # Project configuration
├── tsconfig.json          # TypeScript configuration
├── uno.config.ts          # UnoCSS configuration
└── vite.config.ts         # Vite configuration
```

## 🤝 Contributing

We welcome all forms of contributions! If you want to contribute to the project, please:

1. Fork this repository
2. Create your feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit your changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to the branch (`git push origin feature/AmazingFeature`)
5. Open a Pull Request

### Commit Convention

This project uses conventional commit messages. Please use the following command to commit:

```bash
pnpm commit
```

## 📄 License

This project is open source under the [MIT](./LICENSE) license. You are free to use, modify, and distribute this project.

## 🙏 Acknowledgements

Thank you to all the developers who have contributed to this project!

Special thanks to the following open source projects:
- [Vue.js](https://vuejs.org/)
- [Vite](https://vitejs.dev/)
- [Naive UI](https://www.naiveui.com/)
- [UnoCSS](https://unocss.dev/)
- [Pinia](https://pinia.vuejs.org/)

## 📮 Contact Us

- Email: <EMAIL>
- GitHub Issues: [Submit Issues](https://github.com/qingyun-wallpaper/admin/issues)

## 🌐 Links

- [Live Demo](https://admin.qingyun-wallpaper.com)
- [Documentation](https://docs.qingyun-wallpaper.com)
- [Changelog](./CHANGELOG.md)

---

<div align="center">
  <p>Made with ❤️ by QingYun Wallpaper Team</p>
  <p>© 2024 QingYun Wallpaper. All rights reserved.</p>
</div>
