<script setup lang="ts">
import { ref, reactive, computed, h } from 'vue';
import { NCard, NSpace, NButton, NInput, NDataTable, NTag, NSwitch, NPopconfirm, useMessage, NModal, NForm, NFormItem, NSelect, NDatePicker, NPagination, NInputGroup, NIcon, NDropdown, NAvatar, NText, NDescriptions, NDescriptionsItem, NStatistic, NGrid, NGridItem } from 'naive-ui';
import type { DataTableColumns } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';

const message = useMessage();

// 用户状态枚举
const UserStatus = {
  ACTIVE: 1,
  INACTIVE: 0,
  BANNED: -1
};

const statusOptions = [
  { label: '全部', value: null },
  { label: '正常', value: UserStatus.ACTIVE },
  { label: '禁用', value: UserStatus.INACTIVE },
  { label: '封禁', value: UserStatus.BANNED }
];

const roleOptions = [
  { label: '超级管理员', value: 'super_admin' },
  { label: '管理员', value: 'admin' },
  { label: '普通用户', value: 'user' },
  { label: 'VIP用户', value: 'vip' }
];

// 搜索条件
const searchParams = reactive({
  keyword: '',
  status: null as number | null,
  role: '',
  registerTimeRange: null as [number, number] | null
});

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  total: 100
});

// 用户表单
const showUserModal = ref(false);
const isEdit = ref(false);
const userForm = ref({
  id: '',
  username: '',
  email: '',
  phone: '',
  nickname: '',
  role: 'user',
  status: UserStatus.ACTIVE,
  avatar: ''
});

// 用户详情
const showDetailModal = ref(false);
const currentUser = ref<any>(null);

// 批量操作
const selectedRowKeys = ref<string[]>([]);
const showBatchModal = ref(false);
const batchAction = ref('');

const batchActions = [
  { label: '批量启用', value: 'enable' },
  { label: '批量禁用', value: 'disable' },
  { label: '批量删除', value: 'delete' },
  { label: '批量导出', value: 'export' }
];

// 模拟用户数据
const userData = ref([
  {
    id: '1',
    username: 'admin',
    email: '<EMAIL>',
    phone: '13800138000',
    nickname: '超级管理员',
    role: 'super_admin',
    status: UserStatus.ACTIVE,
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=admin',
    registerTime: '2024-01-01 10:00:00',
    lastLoginTime: '2024-03-20 15:30:00',
    downloadCount: 0,
    uploadCount: 120,
    collectCount: 0,
    loginCount: 156,
    lastLoginIp: '*************',
    deviceInfo: 'Chrome 120.0 / Windows 10',
    vipExpireTime: null,
    tags: ['管理员', '活跃用户']
  },
  {
    id: '2',
    username: 'user001',
    email: '<EMAIL>',
    phone: '13900139001',
    nickname: '普通用户1',
    role: 'user',
    status: UserStatus.ACTIVE,
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=user001',
    registerTime: '2024-02-15 14:20:00',
    lastLoginTime: '2024-03-20 10:15:00',
    downloadCount: 56,
    uploadCount: 3,
    collectCount: 28,
    loginCount: 89,
    lastLoginIp: '*************',
    deviceInfo: 'Safari 17.0 / macOS',
    vipExpireTime: null,
    tags: ['新用户', '活跃']
  },
  {
    id: '3',
    username: 'vip_user',
    email: '<EMAIL>',
    phone: '13700137000',
    nickname: 'VIP用户',
    role: 'vip',
    status: UserStatus.ACTIVE,
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=vip',
    registerTime: '2024-01-20 09:30:00',
    lastLoginTime: '2024-03-19 20:45:00',
    downloadCount: 328,
    uploadCount: 45,
    collectCount: 156,
    loginCount: 234,
    lastLoginIp: '*************',
    deviceInfo: 'Chrome 120.0 / Android',
    vipExpireTime: '2024-12-31 23:59:59',
    tags: ['VIP', '高价值用户', '活跃']
  },
  {
    id: '4',
    username: 'banned_user',
    email: '<EMAIL>',
    phone: '13600136000',
    nickname: '违规用户',
    role: 'user',
    status: UserStatus.BANNED,
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=banned',
    registerTime: '2024-03-01 11:00:00',
    lastLoginTime: '2024-03-15 08:00:00',
    downloadCount: 12,
    uploadCount: 0,
    collectCount: 5,
    loginCount: 23,
    lastLoginIp: '*************',
    deviceInfo: 'Firefox 121.0 / Windows 11',
    vipExpireTime: null,
    tags: ['违规用户', '已封禁']
  },
  {
    id: '5',
    username: 'test_user',
    email: '<EMAIL>',
    phone: '13500135000',
    nickname: '测试用户',
    role: 'user',
    status: UserStatus.INACTIVE,
    avatar: 'https://api.dicebear.com/7.x/avataaars/svg?seed=test',
    registerTime: '2024-03-10 16:20:00',
    lastLoginTime: '2024-03-18 14:30:00',
    downloadCount: 8,
    uploadCount: 1,
    collectCount: 3,
    loginCount: 12,
    lastLoginIp: '*************',
    deviceInfo: 'Edge 120.0 / Windows 10',
    vipExpireTime: null,
    tags: ['测试', '不活跃']
  }
]);

// 过滤后的数据
const filteredData = computed(() => {
  let data = [...userData.value];
  
  // 关键词搜索
  if (searchParams.keyword) {
    data = data.filter(item => 
      item.username.includes(searchParams.keyword) ||
      item.nickname.includes(searchParams.keyword) ||
      item.email.includes(searchParams.keyword) ||
      item.phone.includes(searchParams.keyword)
    );
  }
  
  // 状态筛选
  if (searchParams.status !== null) {
    data = data.filter(item => item.status === searchParams.status);
  }
  
  // 角色筛选
  if (searchParams.role) {
    data = data.filter(item => item.role === searchParams.role);
  }
  
  pagination.total = data.length;
  
  // 分页
  const start = (pagination.page - 1) * pagination.pageSize;
  const end = start + pagination.pageSize;
  
  return data.slice(start, end);
});

// 表格列配置
const columns: DataTableColumns = [
  {
    type: 'selection',
    fixed: 'left'
  },
  {
    title: '用户信息',
    key: 'userInfo',
    width: 280,
    render(row: any) {
      return h('div', { class: 'flex items-center gap-3' }, [
        h(NAvatar, {
          size: 40,
          src: row.avatar,
          fallbackSrc: 'https://api.dicebear.com/7.x/avataaars/svg?seed=default'
        }),
        h('div', [
          h('div', { class: 'font-medium' }, row.nickname),
          h('div', { class: 'text-xs text-gray-500' }, `@${row.username}`)
        ])
      ]);
    }
  },
  {
    title: '联系方式',
    key: 'contact',
    width: 200,
    render(row: any) {
      return h('div', [
        h('div', { class: 'text-sm' }, row.email),
        h('div', { class: 'text-sm text-gray-500' }, row.phone)
      ]);
    }
  },
  {
    title: '角色',
    key: 'role',
    width: 120,
    render(row: any) {
      const roleMap: any = {
        super_admin: { text: '超级管理员', type: 'error' },
        admin: { text: '管理员', type: 'warning' },
        vip: { text: 'VIP用户', type: 'success' },
        user: { text: '普通用户', type: 'default' }
      };
      const role = roleMap[row.role] || { text: '未知', type: 'default' };
      return h(NTag, { type: role.type }, { default: () => role.text });
    }
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    render(row: any) {
      const statusMap: any = {
        [UserStatus.ACTIVE]: { text: '正常', type: 'success' },
        [UserStatus.INACTIVE]: { text: '禁用', type: 'warning' },
        [UserStatus.BANNED]: { text: '封禁', type: 'error' }
      };
      const status = statusMap[row.status] || { text: '未知', type: 'default' };
      return h(NTag, { type: status.type }, { default: () => status.text });
    }
  },
  {
    title: '数据统计',
    key: 'stats',
    width: 180,
    render(row: any) {
      return h('div', { class: 'flex gap-4 text-sm' }, [
        h('span', `下载: ${row.downloadCount}`),
        h('span', `上传: ${row.uploadCount}`),
        h('span', `收藏: ${row.collectCount}`)
      ]);
    }
  },
  {
    title: '注册时间',
    key: 'registerTime',
    width: 180
  },
  {
    title: '最后登录',
    key: 'lastLoginTime',
    width: 180
  },
  {
    title: '操作',
    key: 'actions',
    width: 200,
    fixed: 'right',
    render(row: any) {
      const moreOptions = [
        {
          label: '查看详情',
          key: 'detail',
          icon: () => h(SvgIcon, { icon: 'carbon:view', class: 'text-16px' })
        },
        {
          label: row.status === UserStatus.BANNED ? '解封用户' : '封禁用户',
          key: 'ban',
          icon: () => h(SvgIcon, { icon: 'carbon:user-x', class: 'text-16px' })
        },
        {
          label: '重置密码',
          key: 'reset',
          icon: () => h(SvgIcon, { icon: 'carbon:password', class: 'text-16px' })
        },
        {
          label: '登录日志',
          key: 'logs',
          icon: () => h(SvgIcon, { icon: 'carbon:document', class: 'text-16px' })
        }
      ];
      
      return h(NSpace, null, {
        default: () => [
          h(NButton, {
            size: 'small',
            type: 'primary',
            ghost: true,
            onClick: () => handleEdit(row)
          }, { default: () => '编辑' }),
          h(NPopconfirm, {
            onPositiveClick: () => handleDelete(row)
          }, {
            trigger: () => h(NButton, {
              size: 'small',
              type: 'error',
              ghost: true
            }, { default: () => '删除' }),
            default: () => '确定要删除该用户吗？'
          }),
          h(NDropdown, {
            options: moreOptions,
            onSelect: (key: string) => handleMoreAction(key, row)
          }, {
            default: () => h(NButton, {
              size: 'small',
              quaternary: true
            }, {
              icon: () => h(SvgIcon, { icon: 'carbon:overflow-menu-vertical', class: 'text-16px' })
            })
          })
        ]
      });
    }
  }
];

// 处理搜索
const handleSearch = () => {
  pagination.page = 1;
  message.info('搜索用户');
};

// 重置搜索
const handleReset = () => {
  searchParams.keyword = '';
  searchParams.status = null;
  searchParams.role = '';
  searchParams.registerTimeRange = null;
  pagination.page = 1;
};

// 添加用户
const handleAdd = () => {
  isEdit.value = false;
  userForm.value = {
    id: '',
    username: '',
    email: '',
    phone: '',
    nickname: '',
    role: 'user',
    status: UserStatus.ACTIVE,
    avatar: ''
  };
  showUserModal.value = true;
};

// 编辑用户
const handleEdit = (row: any) => {
  isEdit.value = true;
  userForm.value = { ...row };
  showUserModal.value = true;
};

// 删除用户
const handleDelete = (row: any) => {
  const index = userData.value.findIndex(item => item.id === row.id);
  if (index > -1) {
    userData.value.splice(index, 1);
    message.success('删除成功');
  }
};

// 保存用户
const handleSaveUser = () => {
  if (isEdit.value) {
    const index = userData.value.findIndex(item => item.id === userForm.value.id);
    if (index > -1) {
      userData.value[index] = { ...userData.value[index], ...userForm.value };
    }
    message.success('更新成功');
  } else {
    const newUser = {
      ...userForm.value,
      id: Date.now().toString(),
      registerTime: new Date().toLocaleString(),
      lastLoginTime: new Date().toLocaleString(),
      downloadCount: 0,
      uploadCount: 0,
      collectCount: 0
    };
    userData.value.unshift(newUser);
    message.success('添加成功');
  }
  showUserModal.value = false;
};

// 批量操作处理
const handleBatchAction = () => {
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择要操作的用户');
    return;
  }

  switch (batchAction.value) {
    case 'enable':
      selectedRowKeys.value.forEach(id => {
        const user = userData.value.find(u => u.id === id);
        if (user) user.status = UserStatus.ACTIVE;
      });
      message.success(`已启用 ${selectedRowKeys.value.length} 个用户`);
      break;
    case 'disable':
      selectedRowKeys.value.forEach(id => {
        const user = userData.value.find(u => u.id === id);
        if (user) user.status = UserStatus.INACTIVE;
      });
      message.success(`已禁用 ${selectedRowKeys.value.length} 个用户`);
      break;
    case 'delete':
      userData.value = userData.value.filter(u => !selectedRowKeys.value.includes(u.id));
      message.success(`已删除 ${selectedRowKeys.value.length} 个用户`);
      break;
    case 'export':
      message.success(`已导出 ${selectedRowKeys.value.length} 个用户数据`);
      break;
  }

  selectedRowKeys.value = [];
  showBatchModal.value = false;
};

// 更多操作
const handleMoreAction = (key: string, row: any) => {
  switch (key) {
    case 'detail':
      currentUser.value = row;
      showDetailModal.value = true;
      break;
    case 'ban':
      const isBanned = row.status === UserStatus.BANNED;
      row.status = isBanned ? UserStatus.ACTIVE : UserStatus.BANNED;
      message.success(isBanned ? '解封成功' : '封禁成功');
      break;
    case 'reset':
      message.success('密码重置邮件已发送');
      break;
    case 'logs':
      message.info('查看登录日志功能开发中');
      break;
  }
};

// 导出数据
const handleExport = () => {
  message.info('导出用户数据');
};

// 刷新数据
const handleRefresh = () => {
  message.info('刷新数据');
};
</script>

<template>
  <div class="space-y-4">
    <!-- 统计卡片 -->
    <NGrid :cols="4" :x-gap="16">
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总用户数">
            <template #prefix>
              <SvgIcon icon="carbon:user-multiple" class="text-20px text-primary" />
            </template>
            {{ userData.length }}
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="活跃用户">
            {{ userData.filter(u => u.status === UserStatus.ACTIVE).length }}
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="VIP用户">
            {{ userData.filter(u => u.role === 'vip').length }}
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="今日新增">
            12
          </NStatistic>
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 主卡片 -->
    <NCard title="用户列表" :bordered="false">
      <!-- 搜索区域 -->
      <template #header-extra>
        <NSpace>
          <NButton type="primary" @click="handleAdd">
            <template #icon>
              <SvgIcon icon="carbon:user-avatar" />
            </template>
            添加用户
          </NButton>
          <NButton
            v-if="selectedRowKeys.length > 0"
            type="warning"
            @click="showBatchModal = true"
          >
            <template #icon>
              <SvgIcon icon="carbon:batch-job" />
            </template>
            批量操作 ({{ selectedRowKeys.length }})
          </NButton>
          <NButton @click="handleExport">
            <template #icon>
              <SvgIcon icon="carbon:download" />
            </template>
            导出
          </NButton>
          <NButton @click="handleRefresh">
            <template #icon>
              <SvgIcon icon="carbon:renew" />
            </template>
          </NButton>
        </NSpace>
      </template>

      <NSpace vertical :size="16">
        <!-- 搜索表单 -->
        <NSpace>
          <NInputGroup>
            <NInput 
              v-model:value="searchParams.keyword" 
              placeholder="搜索用户名/昵称/邮箱/手机号"
              style="width: 300px"
              @keyup.enter="handleSearch"
            >
              <template #prefix>
                <SvgIcon icon="carbon:search" class="text-16px" />
              </template>
            </NInput>
          </NInputGroup>
          <NSelect
            v-model:value="searchParams.status"
            :options="statusOptions"
            placeholder="用户状态"
            style="width: 120px"
          />
          <NSelect
            v-model:value="searchParams.role"
            :options="roleOptions"
            placeholder="用户角色"
            clearable
            style="width: 140px"
          />
          <NDatePicker
            v-model:value="searchParams.registerTimeRange"
            type="daterange"
            clearable
            placeholder="注册时间范围"
            style="width: 280px"
          />
          <NButton type="primary" @click="handleSearch">
            <template #icon>
              <SvgIcon icon="carbon:search" />
            </template>
            搜索
          </NButton>
          <NButton @click="handleReset">重置</NButton>
        </NSpace>

        <!-- 数据表格 -->
        <NDataTable
          v-model:checked-row-keys="selectedRowKeys"
          :columns="columns"
          :data="filteredData"
          :row-key="(row: any) => row.id"
          :scroll-x="1600"
          striped
        />

        <!-- 分页 -->
        <div class="flex justify-end">
          <NPagination
            v-model:page="pagination.page"
            v-model:page-size="pagination.pageSize"
            :item-count="pagination.total"
            :page-sizes="[10, 20, 30, 50]"
            show-size-picker
          />
        </div>
      </NSpace>
    </NCard>

    <!-- 用户表单弹窗 -->
    <NModal
      v-model:show="showUserModal"
      :title="isEdit ? '编辑用户' : '添加用户'"
      preset="dialog"
      style="width: 600px"
    >
      <NForm :model="userForm" label-placement="left" label-width="100px">
        <NFormItem label="用户名" path="username">
          <NInput v-model:value="userForm.username" placeholder="请输入用户名" />
        </NFormItem>
        <NFormItem label="昵称" path="nickname">
          <NInput v-model:value="userForm.nickname" placeholder="请输入昵称" />
        </NFormItem>
        <NFormItem label="邮箱" path="email">
          <NInput v-model:value="userForm.email" placeholder="请输入邮箱" />
        </NFormItem>
        <NFormItem label="手机号" path="phone">
          <NInput v-model:value="userForm.phone" placeholder="请输入手机号" />
        </NFormItem>
        <NFormItem label="角色" path="role">
          <NSelect v-model:value="userForm.role" :options="roleOptions" />
        </NFormItem>
        <NFormItem label="状态" path="status">
          <NSelect 
            v-model:value="userForm.status" 
            :options="statusOptions.filter(s => s.value !== null)"
          />
        </NFormItem>
      </NForm>
      <template #action>
        <NSpace justify="end">
          <NButton @click="showUserModal = false">取消</NButton>
          <NButton type="primary" @click="handleSaveUser">保存</NButton>
        </NSpace>
      </template>
    </NModal>

    <!-- 用户详情弹窗 -->
    <NModal
      v-model:show="showDetailModal"
      title="用户详情"
      preset="dialog"
      style="width: 700px"
    >
      <NDescriptions v-if="currentUser" :column="2" label-placement="left">
        <NDescriptionsItem label="用户名">{{ currentUser.username }}</NDescriptionsItem>
        <NDescriptionsItem label="昵称">{{ currentUser.nickname }}</NDescriptionsItem>
        <NDescriptionsItem label="邮箱">{{ currentUser.email }}</NDescriptionsItem>
        <NDescriptionsItem label="手机号">{{ currentUser.phone }}</NDescriptionsItem>
        <NDescriptionsItem label="角色">
          <NTag :type="currentUser.role === 'super_admin' ? 'error' : currentUser.role === 'vip' ? 'success' : 'default'">
            {{ roleOptions.find(r => r.value === currentUser.role)?.label }}
          </NTag>
        </NDescriptionsItem>
        <NDescriptionsItem label="状态">
          <NTag :type="currentUser.status === UserStatus.ACTIVE ? 'success' : currentUser.status === UserStatus.BANNED ? 'error' : 'warning'">
            {{ currentUser.status === UserStatus.ACTIVE ? '正常' : currentUser.status === UserStatus.BANNED ? '封禁' : '禁用' }}
          </NTag>
        </NDescriptionsItem>
        <NDescriptionsItem label="注册时间">{{ currentUser.registerTime }}</NDescriptionsItem>
        <NDescriptionsItem label="最后登录">{{ currentUser.lastLoginTime }}</NDescriptionsItem>
        <NDescriptionsItem label="下载数量">{{ currentUser.downloadCount }}</NDescriptionsItem>
        <NDescriptionsItem label="上传数量">{{ currentUser.uploadCount }}</NDescriptionsItem>
        <NDescriptionsItem label="收藏数量">{{ currentUser.collectCount }}</NDescriptionsItem>
        <NDescriptionsItem label="头像" :span="2">
          <NAvatar :size="60" :src="currentUser.avatar" />
        </NDescriptionsItem>
      </NDescriptions>
    </NModal>

    <!-- 批量操作弹窗 -->
    <NModal
      v-model:show="showBatchModal"
      title="批量操作"
      preset="dialog"
      style="width: 500px"
    >
      <NSpace vertical :size="16">
        <div>
          <div class="mb-2">已选择 {{ selectedRowKeys.length }} 个用户</div>
          <NSelect
            v-model:value="batchAction"
            :options="batchActions"
            placeholder="请选择操作类型"
          />
        </div>
        <div class="text-gray-500 text-sm">
          注意：批量操作将对所有选中的用户生效，请谨慎操作。
        </div>
      </NSpace>
      <template #action>
        <NSpace>
          <NButton @click="showBatchModal = false">取消</NButton>
          <NButton
            type="primary"
            :disabled="!batchAction"
            @click="handleBatchAction"
          >
            确认操作
          </NButton>
        </NSpace>
      </template>
    </NModal>
  </div>
</template>
