<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { 
  NCard, NSpace, NStatistic, NGrid, NGridItem, NDatePicker,
  NSelect, NButton, NTag, NProgress, NList, NListItem, NThing,
  NDescriptions, NDescriptionsItem, NDivider, NIcon
} from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';
import * as echarts from 'echarts';

const { SvgIconVNode } = useSvgIcon();

// 时间范围
const dateRange = ref<[number, number]>([
  new Date().getTime() - 7 * 24 * 3600 * 1000,
  new Date().getTime()
]);

// 统计类型
const statisticsType = ref('download');
const statisticsOptions = [
  { label: '下载统计', value: 'download' },
  { label: '搜索分析', value: 'search' },
  { label: '用户分析', value: 'user' },
  { label: '流量分析', value: 'traffic' }
];

// 模拟数据 - 总体统计
const overallStats = ref({
  totalDownloads: 1258960,
  todayDownloads: 12580,
  weekDownloads: 98650,
  monthDownloads: 456780,
  totalUsers: 125680,
  activeUsers: 8956,
  newUsers: 1256,
  totalWallpapers: 15680,
  avgDownloadPerUser: 10.02,
  avgDownloadPerWallpaper: 80.31
});

// 热门壁纸排行
const popularWallpapers = ref([
  { id: 1, title: '星空之夜', downloads: 8956, category: '风景', trend: 'up', change: 15.2 },
  { id: 2, title: '梦幻森林', downloads: 7862, category: '风景', trend: 'up', change: 8.5 },
  { id: 3, title: '赛博朋克2077', downloads: 6543, category: '游戏', trend: 'down', change: -3.2 },
  { id: 4, title: '初音未来', downloads: 5632, category: '动漫', trend: 'up', change: 12.8 },
  { id: 5, title: '极简主义', downloads: 4521, category: '简约', trend: 'up', change: 5.6 },
  { id: 6, title: '原神影画', downloads: 4236, category: '游戏', trend: 'up', change: 18.9 },
  { id: 7, title: '东京夜景', downloads: 3865, category: '风景', trend: 'down', change: -1.2 },
  { id: 8, title: '机械姬', downloads: 3542, category: '动漫', trend: 'up', change: 7.3 },
  { id: 9, title: '抽象艺术', downloads: 3126, category: '抽象', trend: 'up', change: 4.5 },
  { id: 10, title: '山水画卷', downloads: 2896, category: '风景', trend: 'down', change: -2.8 }
]);

// 热门搜索词
const popularSearches = ref([
  { keyword: '动漫', count: 15680, trend: 'up' },
  { keyword: '风景', count: 12560, trend: 'up' },
  { keyword: '游戏', count: 10230, trend: 'down' },
  { keyword: '简约', count: 8956, trend: 'up' },
  { keyword: '原神', count: 7823, trend: 'up' },
  { keyword: '赛博朋克', count: 6542, trend: 'down' },
  { keyword: '星空', count: 5632, trend: 'up' },
  { keyword: '初音', count: 4523, trend: 'up' },
  { keyword: '抽象', count: 3652, trend: 'down' },
  { keyword: '二次元', count: 3214, trend: 'up' }
]);

// 分类下载占比
const categoryStats = ref([
  { name: '动漫', value: 35.2, downloads: 442896 },
  { name: '风景', value: 28.6, downloads: 359963 },
  { name: '游戏', value: 18.9, downloads: 237943 },
  { name: '简约', value: 8.5, downloads: 107011 },
  { name: '抽象', value: 5.2, downloads: 65465 },
  { name: '其他', value: 3.6, downloads: 45322 }
]);

// 用户活跃度
const userActivityData = ref([
  { date: '01-10', active: 8523, new: 523 },
  { date: '01-11', active: 8956, new: 612 },
  { date: '01-12', active: 9123, new: 489 },
  { date: '01-13', active: 8756, new: 556 },
  { date: '01-14', active: 9234, new: 678 },
  { date: '01-15', active: 9456, new: 723 },
  { date: '01-16', active: 9823, new: 812 }
]);

// 设备分布
const deviceStats = ref([
  { name: 'Android', value: 45.2, color: '#4ade80' },
  { name: 'iOS', value: 32.5, color: '#3b82f6' },
  { name: 'Web', value: 18.3, color: '#f59e0b' },
  { name: '其他', value: 4.0, color: '#94a3b8' }
]);

// 初始化图表
const initCharts = () => {
  // 下载趋势图
  const downloadChart = echarts.init(document.getElementById('download-trend-chart'));
  downloadChart.setOption({
    title: { text: '下载趋势', left: 'center' },
    tooltip: { trigger: 'axis' },
    xAxis: {
      type: 'category',
      data: ['01-10', '01-11', '01-12', '01-13', '01-14', '01-15', '01-16']
    },
    yAxis: { type: 'value' },
    series: [{
      name: '下载量',
      type: 'line',
      smooth: true,
      data: [12580, 13256, 14523, 13896, 15632, 16852, 17236],
      areaStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          { offset: 0, color: 'rgba(59, 130, 246, 0.3)' },
          { offset: 1, color: 'rgba(59, 130, 246, 0.05)' }
        ])
      },
      itemStyle: { color: '#3b82f6' }
    }]
  });

  // 分类占比饼图
  const categoryChart = echarts.init(document.getElementById('category-pie-chart'));
  categoryChart.setOption({
    title: { text: '分类下载占比', left: 'center' },
    tooltip: { trigger: 'item' },
    legend: { bottom: '5%', left: 'center' },
    series: [{
      name: '下载量',
      type: 'pie',
      radius: ['40%', '70%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 2
      },
      label: {
        show: false,
        position: 'center'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: 20,
          fontWeight: 'bold'
        }
      },
      labelLine: { show: false },
      data: categoryStats.value.map(item => ({
        value: item.value,
        name: item.name
      }))
    }]
  });

  // 用户活跃度图
  const userChart = echarts.init(document.getElementById('user-activity-chart'));
  userChart.setOption({
    title: { text: '用户活跃度', left: 'center' },
    tooltip: { trigger: 'axis' },
    legend: { data: ['活跃用户', '新增用户'], top: '10%' },
    xAxis: {
      type: 'category',
      data: userActivityData.value.map(item => item.date)
    },
    yAxis: { type: 'value' },
    series: [
      {
        name: '活跃用户',
        type: 'bar',
        data: userActivityData.value.map(item => item.active),
        itemStyle: { color: '#4ade80' }
      },
      {
        name: '新增用户',
        type: 'line',
        data: userActivityData.value.map(item => item.new),
        itemStyle: { color: '#f472b6' }
      }
    ]
  });

  // 流量来源图
  const trafficChart = echarts.init(document.getElementById('traffic-source-chart'));
  trafficChart.setOption({
    title: { text: '流量来源分布', left: 'center' },
    tooltip: { trigger: 'item' },
    series: [{
      name: '流量来源',
      type: 'pie',
      radius: '60%',
      data: [
        { value: 45, name: '直接访问' },
        { value: 25, name: '搜索引擎' },
        { value: 18, name: '社交媒体' },
        { value: 8, name: '广告推广' },
        { value: 4, name: '其他' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  });

  // 响应式调整
  window.addEventListener('resize', () => {
    downloadChart.resize();
    categoryChart.resize();
    userChart.resize();
    trafficChart.resize();
  });
};

// 刷新数据
const handleRefresh = () => {
  // 模拟刷新数据
  console.log('刷新数据...');
};

// 导出报表
const handleExport = () => {
  console.log('导出报表...');
};

onMounted(() => {
  setTimeout(initCharts, 100);
});
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 操作栏 -->
    <NCard :bordered="false">
      <NSpace justify="space-between">
        <NSpace>
          <NDatePicker
            v-model:value="dateRange"
            type="daterange"
            clearable
            :default-value="dateRange"
          />
          <NSelect
            v-model:value="statisticsType"
            :options="statisticsOptions"
            style="width: 150px"
          />
          <NButton @click="handleRefresh">
            <template #icon>
              <component :is="SvgIconVNode({ icon: 'material-symbols:refresh' })" />
            </template>
            刷新
          </NButton>
        </NSpace>
        <NButton type="primary" @click="handleExport">
          <template #icon>
            <component :is="SvgIconVNode({ icon: 'material-symbols:download' })" />
          </template>
          导出报表
        </NButton>
      </NSpace>
    </NCard>

    <!-- 总体统计 -->
    <NGrid :cols="5" :x-gap="16" :y-gap="16">
      <NGridItem>
        <NCard :bordered="false">
          <NStatistic label="总下载量" :value="overallStats.totalDownloads">
            <template #prefix>
              <NIcon color="#3b82f6" size="20">
                <component :is="SvgIconVNode({ icon: 'material-symbols:download' })" />
              </NIcon>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard :bordered="false">
          <NStatistic label="今日下载" :value="overallStats.todayDownloads">
            <template #suffix>
              <NTag type="success" size="small">
                <component :is="SvgIconVNode({ icon: 'material-symbols:trending-up' })" />
                12.5%
              </NTag>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard :bordered="false">
          <NStatistic label="总用户数" :value="overallStats.totalUsers">
            <template #prefix>
              <NIcon color="#4ade80" size="20">
                <component :is="SvgIconVNode({ icon: 'material-symbols:group' })" />
              </NIcon>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard :bordered="false">
          <NStatistic label="活跃用户" :value="overallStats.activeUsers">
            <template #suffix>
              <span class="text-12px text-gray-500">7日内</span>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard :bordered="false">
          <NStatistic label="壁纸总数" :value="overallStats.totalWallpapers">
            <template #prefix>
              <NIcon color="#f59e0b" size="20">
                <component :is="SvgIconVNode({ icon: 'material-symbols:image' })" />
              </NIcon>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 图表区域 -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <NGridItem>
        <NCard :bordered="false" title="下载趋势">
          <div id="download-trend-chart" style="height: 300px"></div>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard :bordered="false" title="分类占比">
          <div id="category-pie-chart" style="height: 300px"></div>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard :bordered="false" title="用户活跃度">
          <div id="user-activity-chart" style="height: 300px"></div>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard :bordered="false" title="流量来源">
          <div id="traffic-source-chart" style="height: 300px"></div>
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 排行榜 -->
    <NGrid :cols="3" :x-gap="16">
      <!-- 热门壁纸 -->
      <NGridItem>
        <NCard :bordered="false" title="热门壁纸TOP10">
          <NList>
            <NListItem v-for="(item, index) in popularWallpapers" :key="item.id">
              <NThing>
                <template #header>
                  <NSpace align="center">
                    <NTag :type="index < 3 ? 'error' : 'default'" size="small">
                      {{ index + 1 }}
                    </NTag>
                    <span>{{ item.title }}</span>
                  </NSpace>
                </template>
                <template #description>
                  <NSpace>
                    <NTag type="info" size="small">{{ item.category }}</NTag>
                    <span class="text-gray-500">{{ item.downloads.toLocaleString() }} 次</span>
                    <NTag 
                      :type="item.trend === 'up' ? 'success' : 'warning'"
                      size="small"
                    >
                      <component :is="SvgIconVNode({ 
                        icon: item.trend === 'up' ? 'material-symbols:trending-up' : 'material-symbols:trending-down' 
                      })" />
                      {{ Math.abs(item.change) }}%
                    </NTag>
                  </NSpace>
                </template>
              </NThing>
            </NListItem>
          </NList>
        </NCard>
      </NGridItem>

      <!-- 热门搜索 -->
      <NGridItem>
        <NCard :bordered="false" title="热门搜索词TOP10">
          <NList>
            <NListItem v-for="(item, index) in popularSearches" :key="item.keyword">
              <NSpace justify="space-between" class="w-full">
                <NSpace>
                  <NTag :type="index < 3 ? 'error' : 'default'" size="small">
                    {{ index + 1 }}
                  </NTag>
                  <span>{{ item.keyword }}</span>
                </NSpace>
                <NSpace>
                  <span class="text-gray-500">{{ item.count.toLocaleString() }}</span>
                  <NIcon 
                    :color="item.trend === 'up' ? '#4ade80' : '#f59e0b'"
                    size="16"
                  >
                    <component :is="SvgIconVNode({ 
                      icon: item.trend === 'up' ? 'material-symbols:trending-up' : 'material-symbols:trending-down' 
                    })" />
                  </NIcon>
                </NSpace>
              </NSpace>
            </NListItem>
          </NList>
        </NCard>
      </NGridItem>

      <!-- 设备分布 -->
      <NGridItem>
        <NCard :bordered="false" title="设备分布">
          <NSpace vertical :size="12">
            <div v-for="device in deviceStats" :key="device.name">
              <NSpace justify="space-between" class="mb-4px">
                <span>{{ device.name }}</span>
                <span class="text-gray-500">{{ device.value }}%</span>
              </NSpace>
              <NProgress
                type="line"
                :percentage="device.value"
                :color="device.color"
                :show-indicator="false"
              />
            </div>
          </NSpace>
          <NDivider />
          <NDescriptions :column="1" size="small">
            <NDescriptionsItem label="人均下载">
              {{ overallStats.avgDownloadPerUser.toFixed(2) }} 次
            </NDescriptionsItem>
            <NDescriptionsItem label="壁纸均下载">
              {{ overallStats.avgDownloadPerWallpaper.toFixed(2) }} 次
            </NDescriptionsItem>
            <NDescriptionsItem label="本周新增">
              {{ overallStats.newUsers.toLocaleString() }} 用户
            </NDescriptionsItem>
          </NDescriptions>
        </NCard>
      </NGridItem>
    </NGrid>
  </NSpace>
</template>

<style scoped>
</style>
