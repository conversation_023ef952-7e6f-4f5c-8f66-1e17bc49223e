<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';
import * as echarts from 'echarts';
import { useThemeStore } from '@/store/modules/theme';

interface Props {
  option: echarts.EChartsOption;
  width?: string | number;
  height?: string | number;
  loading?: boolean;
  theme?: string;
  autoResize?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '400px',
  loading: false,
  theme: 'default',
  autoResize: true
});

const emit = defineEmits<{
  chartReady: [chart: echarts.ECharts];
  chartClick: [params: any];
  chartHover: [params: any];
}>();

const chartRef = ref<HTMLDivElement>();
const themeStore = useThemeStore();

let chartInstance: echarts.ECharts | null = null;
let resizeObserver: ResizeObserver | null = null;

// 初始化图表
const initChart = async () => {
  if (!chartRef.value) return;

  await nextTick();
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose();
  }

  // 创建新实例
  chartInstance = echarts.init(chartRef.value, getTheme());
  
  // 设置配置项
  chartInstance.setOption(props.option);
  
  // 绑定事件
  chartInstance.on('click', (params) => {
    emit('chartClick', params);
  });
  
  chartInstance.on('mouseover', (params) => {
    emit('chartHover', params);
  });

  // 发出图表就绪事件
  emit('chartReady', chartInstance);

  // 设置加载状态
  if (props.loading) {
    chartInstance.showLoading();
  }
};

// 获取主题
const getTheme = () => {
  if (props.theme !== 'default') {
    return props.theme;
  }
  return themeStore.darkMode ? 'dark' : 'light';
};

// 更新图表配置
const updateChart = () => {
  if (chartInstance && props.option) {
    chartInstance.setOption(props.option, true);
  }
};

// 调整图表大小
const resizeChart = () => {
  if (chartInstance) {
    chartInstance.resize();
  }
};

// 设置加载状态
const setLoading = (loading: boolean) => {
  if (!chartInstance) return;
  
  if (loading) {
    chartInstance.showLoading();
  } else {
    chartInstance.hideLoading();
  }
};

// 监听配置变化
watch(() => props.option, updateChart, { deep: true });

// 监听加载状态变化
watch(() => props.loading, setLoading);

// 监听主题变化
watch(() => themeStore.darkMode, () => {
  initChart();
});

// 设置自动调整大小
const setupAutoResize = () => {
  if (!props.autoResize || !chartRef.value) return;

  resizeObserver = new ResizeObserver(() => {
    resizeChart();
  });
  
  resizeObserver.observe(chartRef.value);
  
  // 监听窗口大小变化
  window.addEventListener('resize', resizeChart);
};

// 清理资源
const cleanup = () => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
  
  if (resizeObserver) {
    resizeObserver.disconnect();
    resizeObserver = null;
  }
  
  window.removeEventListener('resize', resizeChart);
};

// 暴露方法给父组件
defineExpose({
  getChart: () => chartInstance,
  resize: resizeChart,
  setOption: (option: echarts.EChartsOption, notMerge?: boolean) => {
    if (chartInstance) {
      chartInstance.setOption(option, notMerge);
    }
  }
});

onMounted(() => {
  initChart();
  setupAutoResize();
});

onUnmounted(() => {
  cleanup();
});
</script>

<template>
  <div
    ref="chartRef"
    :style="{
      width: typeof width === 'number' ? `${width}px` : width,
      height: typeof height === 'number' ? `${height}px` : height
    }"
    class="chart-container"
  />
</template>

<style scoped>
.chart-container {
  min-height: 200px;
}
</style>
