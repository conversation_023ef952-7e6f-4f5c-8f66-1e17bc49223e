import { request } from '../request';

/**
 * 数据分析相关API
 */

// 时间范围类型
export type TimeRange = 'today' | 'week' | 'month' | 'quarter' | 'year' | 'custom';

// 图表数据类型
export interface ChartDataPoint {
  date: string;
  value: number;
  label?: string;
}

// 饼图数据类型
export interface PieChartData {
  name: string;
  value: number;
  percentage: number;
  color?: string;
}

// 统计概览数据
export interface OverviewStats {
  totalDownloads: number;
  totalUploads: number;
  totalUsers: number;
  totalViews: number;
  downloadGrowth: number;
  uploadGrowth: number;
  userGrowth: number;
  viewGrowth: number;
}

// 下载分析数据
export interface DownloadAnalytics {
  totalDownloads: number;
  todayDownloads: number;
  weekDownloads: number;
  monthDownloads: number;
  trendData: ChartDataPoint[];
  categoryDistribution: PieChartData[];
  topWallpapers: Array<{
    id: string;
    title: string;
    downloads: number;
    thumbnail: string;
  }>;
}

// 用户分析数据
export interface UserAnalytics {
  totalUsers: number;
  activeUsers: number;
  newUsers: number;
  retentionRate: number;
  userGrowthTrend: ChartDataPoint[];
  userDistribution: {
    byRegion: PieChartData[];
    byDevice: PieChartData[];
    byAge: PieChartData[];
    byGender: PieChartData[];
  };
  activityHeatmap: Array<{
    hour: number;
    day: number;
    value: number;
  }>;
}

// 搜索分析数据
export interface SearchAnalytics {
  totalSearches: number;
  todaySearches: number;
  avgSearchTime: number;
  successRate: number;
  trendData: ChartDataPoint[];
  hotKeywords: Array<{
    keyword: string;
    count: number;
    growth: number;
    rank: number;
  }>;
  searchSources: PieChartData[];
}

// 流量分析数据
export interface TrafficAnalytics {
  totalViews: number;
  uniqueVisitors: number;
  bounceRate: number;
  avgSessionDuration: number;
  trafficSources: PieChartData[];
  deviceStats: PieChartData[];
  browserStats: PieChartData[];
  geographicData: Array<{
    country: string;
    visitors: number;
    percentage: number;
  }>;
}

/**
 * 获取统计概览数据
 */
export function fetchOverviewStats(timeRange: TimeRange = 'week') {
  return request<OverviewStats>({
    url: '/analytics/overview',
    method: 'get',
    params: { timeRange }
  });
}

/**
 * 获取下载分析数据
 */
export function fetchDownloadAnalytics(timeRange: TimeRange = 'week') {
  return request<DownloadAnalytics>({
    url: '/analytics/download',
    method: 'get',
    params: { timeRange }
  });
}

/**
 * 获取用户分析数据
 */
export function fetchUserAnalytics(timeRange: TimeRange = 'week') {
  return request<UserAnalytics>({
    url: '/analytics/user',
    method: 'get',
    params: { timeRange }
  });
}

/**
 * 获取搜索分析数据
 */
export function fetchSearchAnalytics(timeRange: TimeRange = 'week') {
  return request<SearchAnalytics>({
    url: '/analytics/search',
    method: 'get',
    params: { timeRange }
  });
}

/**
 * 获取流量分析数据
 */
export function fetchTrafficAnalytics(timeRange: TimeRange = 'week') {
  return request<TrafficAnalytics>({
    url: '/analytics/traffic',
    method: 'get',
    params: { timeRange }
  });
}

/**
 * 导出分析报告
 */
export function exportAnalyticsReport(params: {
  type: 'download' | 'user' | 'search' | 'traffic' | 'overview';
  timeRange: TimeRange;
  format: 'excel' | 'pdf' | 'csv';
}) {
  return request<{ downloadUrl: string }>({
    url: '/analytics/export',
    method: 'post',
    data: params
  });
}

/**
 * 获取实时数据
 */
export function fetchRealTimeData() {
  return request<{
    onlineUsers: number;
    todayDownloads: number;
    todayUploads: number;
    todayRegistrations: number;
    recentActivities: Array<{
      id: string;
      type: 'download' | 'upload' | 'register' | 'login';
      user: string;
      content?: string;
      timestamp: string;
    }>;
  }>({
    url: '/analytics/realtime',
    method: 'get'
  });
}

// Mock数据生成器
export function generateMockAnalyticsData() {
  const now = new Date();
  const days = 30;
  
  // 生成趋势数据
  const generateTrendData = (baseValue: number, variance: number): ChartDataPoint[] => {
    const data: ChartDataPoint[] = [];
    for (let i = days; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 3600 * 1000);
      const value = baseValue + Math.random() * variance - variance / 2;
      data.push({
        date: date.toISOString().split('T')[0],
        value: Math.max(0, Math.floor(value))
      });
    }
    return data;
  };

  // 生成饼图数据
  const generatePieData = (categories: string[], total: number): PieChartData[] => {
    const data: PieChartData[] = [];
    let remaining = total;
    
    categories.forEach((name, index) => {
      const isLast = index === categories.length - 1;
      const value = isLast ? remaining : Math.floor(Math.random() * remaining * 0.4);
      const percentage = (value / total) * 100;
      
      data.push({
        name,
        value,
        percentage: Math.round(percentage * 100) / 100
      });
      
      remaining -= value;
    });
    
    return data;
  };

  return {
    overview: {
      totalDownloads: 1256789,
      totalUploads: 45678,
      totalUsers: 89234,
      totalViews: 2345678,
      downloadGrowth: 12.5,
      uploadGrowth: 8.3,
      userGrowth: 15.2,
      viewGrowth: 9.7
    } as OverviewStats,

    download: {
      totalDownloads: 1256789,
      todayDownloads: 3456,
      weekDownloads: 23456,
      monthDownloads: 98765,
      trendData: generateTrendData(3000, 1000),
      categoryDistribution: generatePieData(['风景', '动漫', '抽象', '动物', '建筑'], 100000),
      topWallpapers: [
        { id: '1', title: '美丽风景', downloads: 12345, thumbnail: 'https://picsum.photos/100/100?random=1' },
        { id: '2', title: '动漫少女', downloads: 9876, thumbnail: 'https://picsum.photos/100/100?random=2' },
        { id: '3', title: '抽象艺术', downloads: 8765, thumbnail: 'https://picsum.photos/100/100?random=3' }
      ]
    } as DownloadAnalytics,

    user: {
      totalUsers: 89234,
      activeUsers: 45678,
      newUsers: 1234,
      retentionRate: 68.5,
      userGrowthTrend: generateTrendData(100, 50),
      userDistribution: {
        byRegion: generatePieData(['北京', '上海', '广州', '深圳', '其他'], 89234),
        byDevice: generatePieData(['iOS', 'Android', 'Web', '其他'], 89234),
        byAge: generatePieData(['18-24', '25-30', '31-40', '40+'], 89234),
        byGender: generatePieData(['男性', '女性'], 89234)
      },
      activityHeatmap: []
    } as UserAnalytics,

    search: {
      totalSearches: 234567,
      todaySearches: 892,
      avgSearchTime: 2.3,
      successRate: 95.6,
      trendData: generateTrendData(800, 200),
      hotKeywords: [
        { keyword: '风景', count: 12345, growth: 15.2, rank: 1 },
        { keyword: '动漫', count: 9876, growth: 8.7, rank: 2 },
        { keyword: '抽象', count: 7654, growth: -2.1, rank: 3 }
      ],
      searchSources: generatePieData(['搜索框', '分类浏览', '推荐', '其他'], 234567)
    } as SearchAnalytics,

    traffic: {
      totalViews: 2345678,
      uniqueVisitors: 156789,
      bounceRate: 32.5,
      avgSessionDuration: 245,
      trafficSources: generatePieData(['直接访问', '搜索引擎', '社交媒体', '广告', '其他'], 2345678),
      deviceStats: generatePieData(['桌面端', '移动端', '平板'], 2345678),
      browserStats: generatePieData(['Chrome', 'Safari', 'Firefox', 'Edge', '其他'], 2345678),
      geographicData: [
        { country: '中国', visitors: 123456, percentage: 78.7 },
        { country: '美国', visitors: 12345, percentage: 7.9 },
        { country: '日本', visitors: 8765, percentage: 5.6 }
      ]
    } as TrafficAnalytics
  };
}
