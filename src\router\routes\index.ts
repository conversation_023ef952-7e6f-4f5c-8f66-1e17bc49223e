import type { CustomRoute, ElegantConstRoute, ElegantRoute } from '@elegant-router/types';
import { generatedRoutes } from '../elegant/routes';
import { layouts, views } from '../elegant/imports';
import { transformElegantRoutesToVueRoutes } from '../elegant/transform';

// 添加自定义视图
const customViews = {
  wallpaper_list: () => import('@/views/wallpaper/list/index.vue'),
  wallpaper_upload: () => import('@/views/wallpaper/upload/index.vue')
};

/**
 * custom routes
 *
 * @link https://github.com/soybeanjs/elegant-router?tab=readme-ov-file#custom-route
 */
const customRoutes: CustomRoute[] = [
  {
    name: 'wallpaper',
    path: '/wallpaper',
    component: 'layout.base',
    meta: {
      title: '壁纸管理',
      icon: 'material-symbols:image',
      order: 2
    },
    children: [
      {
        name: 'wallpaper_list',
        path: '/wallpaper/list',
        component: 'view.wallpaper_list',
        meta: {
          title: '壁纸列表',
          icon: 'material-symbols:photo-library'
        }
      },
      {
        name: 'wallpaper_upload',
        path: '/wallpaper/upload',
        component: 'view.wallpaper_upload',
        meta: {
          title: '上传壁纸',
          icon: 'material-symbols:cloud-upload'
        }
      }
    ]
  }
];

/** create routes when the auth route mode is static */
export function createStaticRoutes() {
  const constantRoutes: ElegantRoute[] = [];

  const authRoutes: ElegantRoute[] = [];

  [...customRoutes, ...generatedRoutes].forEach(item => {
    if (item.meta?.constant) {
      constantRoutes.push(item);
    } else {
      authRoutes.push(item);
    }
  });

  return {
    constantRoutes,
    authRoutes
  };
}

/**
 * Get auth vue routes
 *
 * @param routes Elegant routes
 */
export function getAuthVueRoutes(routes: ElegantConstRoute[]) {
  // 合并自定义视图和默认视图
  const allViews = { ...views, ...customViews };
  return transformElegantRoutesToVueRoutes(routes, layouts, allViews);
}
