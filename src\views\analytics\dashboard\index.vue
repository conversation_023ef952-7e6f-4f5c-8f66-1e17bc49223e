<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue';
import { 
  NCard, NSpace, NStatistic, NGrid, NGridItem, NDatePicker,
  NSelect, NButton, NTag, NProgress, NList, NListItem, NThing,
  NDescriptions, NDescriptionsItem, NDivider, NIcon, NSwitch,
  NAlert, NTime, NNumberAnimation
} from 'naive-ui';
import * as echarts from 'echarts';
import { generateMockAnalyticsData } from '@/service/api/analytics';
import SvgIcon from '@/components/custom/svg-icon.vue';
import { useSvgIcon } from '@/hooks/common/icon';

const { SvgIconVNode } = useSvgIcon();

// 时间范围选项
const timeRangeOptions = [
  { label: '今天', value: 'today' },
  { label: '本周', value: 'week' },
  { label: '本月', value: 'month' },
  { label: '本季度', value: 'quarter' },
  { label: '本年', value: 'year' },
  { label: '自定义', value: 'custom' }
];

// 当前选择的时间范围
const selectedTimeRange = ref('week');
const customDateRange = ref<[number, number] | null>(null);

// 实时数据开关
const realTimeEnabled = ref(true);

// Mock数据
const mockData = generateMockAnalyticsData();

// 统计数据
const overviewStats = reactive({
  totalDownloads: mockData.overview.totalDownloads,
  totalUploads: mockData.overview.totalUploads,
  totalUsers: mockData.overview.totalUsers,
  totalViews: mockData.overview.totalViews,
  downloadGrowth: mockData.overview.downloadGrowth,
  uploadGrowth: mockData.overview.uploadGrowth,
  userGrowth: mockData.overview.userGrowth,
  viewGrowth: mockData.overview.viewGrowth
});

// 实时数据
const realTimeData = reactive({
  onlineUsers: 1234,
  todayDownloads: 5678,
  todayUploads: 234,
  todayRegistrations: 89,
  lastUpdateTime: new Date()
});

// 最近活动
const recentActivities = ref([
  { id: '1', type: 'download', user: '用户001', content: '下载了壁纸：美丽风景', timestamp: '2分钟前' },
  { id: '2', type: 'upload', user: '用户002', content: '上传了壁纸：动漫少女', timestamp: '5分钟前' },
  { id: '3', type: 'register', user: '用户003', content: '新用户注册', timestamp: '8分钟前' },
  { id: '4', type: 'download', user: '用户004', content: '下载了壁纸：抽象艺术', timestamp: '12分钟前' },
  { id: '5', type: 'login', user: '用户005', content: '用户登录', timestamp: '15分钟前' }
]);

// 热门内容
const popularContent = ref([
  { id: '1', title: '美丽风景壁纸', downloads: 12345, trend: 'up', growth: 15.2 },
  { id: '2', title: '动漫少女壁纸', downloads: 9876, trend: 'up', growth: 8.7 },
  { id: '3', title: '抽象艺术壁纸', downloads: 8765, trend: 'down', growth: -2.1 },
  { id: '4', title: '城市夜景壁纸', downloads: 7654, trend: 'up', growth: 12.3 },
  { id: '5', title: '自然风光壁纸', downloads: 6543, trend: 'up', growth: 5.8 }
]);

// 图表实例
let downloadChart: echarts.ECharts;
let userChart: echarts.ECharts;
let categoryChart: echarts.ECharts;
let trafficChart: echarts.ECharts;

// 初始化图表
const initCharts = () => {
  // 下载趋势图
  const downloadChartDom = document.getElementById('download-trend-chart');
  if (downloadChartDom) {
    downloadChart = echarts.init(downloadChartDom);
    downloadChart.setOption({
      title: { text: '下载趋势', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: mockData.download.trendData.map(item => item.date.split('-').slice(1).join('/'))
      },
      yAxis: { type: 'value' },
      series: [{
        name: '下载量',
        type: 'line',
        data: mockData.download.trendData.map(item => item.value),
        smooth: true,
        itemStyle: { color: '#2080f0' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(32, 128, 240, 0.3)' },
            { offset: 1, color: 'rgba(32, 128, 240, 0.1)' }
          ])
        }
      }]
    });
  }

  // 用户增长图
  const userChartDom = document.getElementById('user-growth-chart');
  if (userChartDom) {
    userChart = echarts.init(userChartDom);
    userChart.setOption({
      title: { text: '用户增长', left: 'center' },
      tooltip: { trigger: 'axis' },
      xAxis: {
        type: 'category',
        data: mockData.user.userGrowthTrend.map(item => item.date.split('-').slice(1).join('/'))
      },
      yAxis: { type: 'value' },
      series: [{
        name: '新增用户',
        type: 'bar',
        data: mockData.user.userGrowthTrend.map(item => item.value),
        itemStyle: { color: '#18a058' }
      }]
    });
  }

  // 分类分布图
  const categoryChartDom = document.getElementById('category-distribution-chart');
  if (categoryChartDom) {
    categoryChart = echarts.init(categoryChartDom);
    categoryChart.setOption({
      title: { text: '分类分布', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        name: '下载量',
        type: 'pie',
        radius: '60%',
        data: mockData.download.categoryDistribution.map(item => ({
          name: item.name,
          value: item.value
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    });
  }

  // 流量来源图
  const trafficChartDom = document.getElementById('traffic-source-chart');
  if (trafficChartDom) {
    trafficChart = echarts.init(trafficChartDom);
    trafficChart.setOption({
      title: { text: '流量来源', left: 'center' },
      tooltip: { trigger: 'item' },
      series: [{
        name: '访问量',
        type: 'pie',
        radius: ['40%', '70%'],
        data: mockData.traffic.trafficSources.map(item => ({
          name: item.name,
          value: item.value
        })),
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    });
  }

  // 响应式调整
  window.addEventListener('resize', () => {
    downloadChart?.resize();
    userChart?.resize();
    categoryChart?.resize();
    trafficChart?.resize();
  });
};

// 刷新数据
const refreshData = () => {
  realTimeData.lastUpdateTime = new Date();
  realTimeData.onlineUsers = Math.floor(Math.random() * 2000) + 1000;
  realTimeData.todayDownloads += Math.floor(Math.random() * 10);
  realTimeData.todayUploads += Math.floor(Math.random() * 3);
  realTimeData.todayRegistrations += Math.floor(Math.random() * 2);
};

// 导出报告
const exportReport = () => {
  console.log('导出分析报告...');
};

// 实时数据更新
let realTimeInterval: NodeJS.Timeout;

const startRealTimeUpdate = () => {
  if (realTimeEnabled.value) {
    realTimeInterval = setInterval(refreshData, 5000);
  }
};

const stopRealTimeUpdate = () => {
  if (realTimeInterval) {
    clearInterval(realTimeInterval);
  }
};

// 监听实时数据开关
const handleRealTimeToggle = (value: boolean) => {
  if (value) {
    startRealTimeUpdate();
  } else {
    stopRealTimeUpdate();
  }
};

// 获取活动类型图标
const getActivityIcon = (type: string) => {
  const iconMap = {
    download: 'carbon:download',
    upload: 'carbon:upload',
    register: 'carbon:user-avatar',
    login: 'carbon:login'
  };
  return iconMap[type as keyof typeof iconMap] || 'carbon:circle-dash';
};

// 获取活动类型颜色
const getActivityColor = (type: string) => {
  const colorMap = {
    download: 'primary',
    upload: 'success',
    register: 'warning',
    login: 'info'
  };
  return colorMap[type as keyof typeof colorMap] || 'default';
};

onMounted(() => {
  initCharts();
  if (realTimeEnabled.value) {
    startRealTimeUpdate();
  }
});
</script>

<template>
  <div class="space-y-4">
    <!-- 控制面板 -->
    <NCard :bordered="false" size="small">
      <NSpace justify="space-between" align="center">
        <NSpace align="center">
          <span class="text-sm font-medium">时间范围：</span>
          <NSelect
            v-model:value="selectedTimeRange"
            :options="timeRangeOptions"
            style="width: 120px"
            size="small"
          />
          <NDatePicker
            v-if="selectedTimeRange === 'custom'"
            v-model:value="customDateRange"
            type="daterange"
            size="small"
            style="width: 240px"
          />
        </NSpace>
        
        <NSpace align="center">
          <span class="text-sm">实时数据：</span>
          <NSwitch
            v-model:value="realTimeEnabled"
            @update:value="handleRealTimeToggle"
          />
          <NButton type="primary" size="small" @click="exportReport">
            <template #icon>
              <SvgIcon icon="carbon:download" />
            </template>
            导出报告
          </NButton>
        </NSpace>
      </NSpace>
    </NCard>

    <!-- 实时数据提醒 -->
    <NAlert v-if="realTimeEnabled" type="info" size="small">
      <template #icon>
        <SvgIcon icon="carbon:time" />
      </template>
      实时数据已开启，最后更新时间：<NTime :time="realTimeData.lastUpdateTime" format="HH:mm:ss" />
    </NAlert>

    <!-- 统计卡片 -->
    <NGrid :cols="4" :x-gap="16">
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总下载量">
            <template #prefix>
              <SvgIcon icon="carbon:download" class="text-20px text-blue-500" />
            </template>
            <NNumberAnimation
              :from="0"
              :to="overviewStats.totalDownloads"
              :duration="2000"
              show-separator
            />
            <template #suffix>
              <NTag
                :type="overviewStats.downloadGrowth > 0 ? 'success' : 'error'"
                size="small"
                class="ml-2"
              >
                {{ overviewStats.downloadGrowth > 0 ? '+' : '' }}{{ overviewStats.downloadGrowth }}%
              </NTag>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总用户数">
            <template #prefix>
              <SvgIcon icon="carbon:user-multiple" class="text-20px text-green-500" />
            </template>
            <NNumberAnimation
              :from="0"
              :to="overviewStats.totalUsers"
              :duration="2000"
              show-separator
            />
            <template #suffix>
              <NTag
                :type="overviewStats.userGrowth > 0 ? 'success' : 'error'"
                size="small"
                class="ml-2"
              >
                {{ overviewStats.userGrowth > 0 ? '+' : '' }}{{ overviewStats.userGrowth }}%
              </NTag>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总上传量">
            <template #prefix>
              <SvgIcon icon="carbon:upload" class="text-20px text-orange-500" />
            </template>
            <NNumberAnimation
              :from="0"
              :to="overviewStats.totalUploads"
              :duration="2000"
              show-separator
            />
            <template #suffix>
              <NTag
                :type="overviewStats.uploadGrowth > 0 ? 'success' : 'error'"
                size="small"
                class="ml-2"
              >
                {{ overviewStats.uploadGrowth > 0 ? '+' : '' }}{{ overviewStats.uploadGrowth }}%
              </NTag>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="总浏览量">
            <template #prefix>
              <SvgIcon icon="carbon:view" class="text-20px text-purple-500" />
            </template>
            <NNumberAnimation
              :from="0"
              :to="overviewStats.totalViews"
              :duration="2000"
              show-separator
            />
            <template #suffix>
              <NTag
                :type="overviewStats.viewGrowth > 0 ? 'success' : 'error'"
                size="small"
                class="ml-2"
              >
                {{ overviewStats.viewGrowth > 0 ? '+' : '' }}{{ overviewStats.viewGrowth }}%
              </NTag>
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 实时数据卡片 -->
    <NGrid v-if="realTimeEnabled" :cols="4" :x-gap="16">
      <NGridItem>
        <NCard size="small">
          <NStatistic label="在线用户" :value="realTimeData.onlineUsers">
            <template #prefix>
              <SvgIcon icon="carbon:user-online" class="text-16px text-green-500" />
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="今日下载" :value="realTimeData.todayDownloads">
            <template #prefix>
              <SvgIcon icon="carbon:download" class="text-16px text-blue-500" />
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="今日上传" :value="realTimeData.todayUploads">
            <template #prefix>
              <SvgIcon icon="carbon:upload" class="text-16px text-orange-500" />
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard size="small">
          <NStatistic label="今日注册" :value="realTimeData.todayRegistrations">
            <template #prefix>
              <SvgIcon icon="carbon:user-avatar" class="text-16px text-purple-500" />
            </template>
          </NStatistic>
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 图表区域 -->
    <NGrid :cols="2" :x-gap="16" :y-gap="16">
      <NGridItem>
        <NCard title="下载趋势" :bordered="false" size="small">
          <div id="download-trend-chart" style="height: 300px"></div>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard title="用户增长" :bordered="false" size="small">
          <div id="user-growth-chart" style="height: 300px"></div>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard title="分类分布" :bordered="false" size="small">
          <div id="category-distribution-chart" style="height: 300px"></div>
        </NCard>
      </NGridItem>
      <NGridItem>
        <NCard title="流量来源" :bordered="false" size="small">
          <div id="traffic-source-chart" style="height: 300px"></div>
        </NCard>
      </NGridItem>
    </NGrid>

    <!-- 活动和热门内容 -->
    <NGrid :cols="2" :x-gap="16">
      <NGridItem>
        <NCard title="实时活动" :bordered="false" size="small">
          <template #header-extra>
            <NTag size="small" type="success">
              <template #icon>
                <SvgIcon icon="carbon:dot-mark" />
              </template>
              实时
            </NTag>
          </template>
          <NList>
            <NListItem v-for="activity in recentActivities" :key="activity.id">
              <NThing>
                <template #avatar>
                  <NIcon :color="getActivityColor(activity.type)" size="20">
                    <SvgIcon :icon="getActivityIcon(activity.type)" />
                  </NIcon>
                </template>
                <template #header>
                  <span class="text-sm font-medium">{{ activity.user }}</span>
                </template>
                <template #description>
                  <span class="text-xs text-gray-500">{{ activity.content }}</span>
                </template>
                <template #footer>
                  <span class="text-xs text-gray-400">{{ activity.timestamp }}</span>
                </template>
              </NThing>
            </NListItem>
          </NList>
        </NCard>
      </NGridItem>

      <NGridItem>
        <NCard title="热门内容" :bordered="false" size="small">
          <template #header-extra>
            <NButton text size="small">
              查看更多
              <template #icon>
                <SvgIcon icon="carbon:arrow-right" />
              </template>
            </NButton>
          </template>
          <NSpace vertical :size="12">
            <div
              v-for="(item, index) in popularContent"
              :key="item.id"
              class="flex items-center justify-between p-3 rounded-lg bg-gray-50 dark:bg-gray-800"
            >
              <div class="flex items-center space-x-3">
                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-primary text-white text-sm font-bold">
                  {{ index + 1 }}
                </div>
                <div>
                  <div class="text-sm font-medium">{{ item.title }}</div>
                  <div class="text-xs text-gray-500">{{ item.downloads.toLocaleString() }} 次下载</div>
                </div>
              </div>
              <div class="flex items-center space-x-2">
                <NTag
                  :type="item.trend === 'up' ? 'success' : 'error'"
                  size="small"
                >
                  <template #icon>
                    <SvgIcon
                      :icon="item.trend === 'up' ? 'carbon:trending-up' : 'carbon:trending-down'"
                    />
                  </template>
                  {{ item.growth > 0 ? '+' : '' }}{{ item.growth }}%
                </NTag>
              </div>
            </div>
          </NSpace>
        </NCard>
      </NGridItem>
    </NGrid>
  </div>
</template>
