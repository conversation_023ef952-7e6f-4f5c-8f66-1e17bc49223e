<script setup lang="ts">
import { ref, computed, h } from 'vue';
import { 
  NCard, NSpace, NButton, NImage, NTag, NModal, NForm, NFormItem,
  NInput, NInputNumber, useMessage, useDialog, NDataTable, NSwitch,
  NColorPicker, NIcon, NTree, NEmpty, NSelect
} from 'naive-ui';
import type { DataTableColumns, TreeOption } from 'naive-ui';
import { useSvgIcon } from '@/hooks/common/icon';

const { SvgIconVNode } = useSvgIcon();
const message = useMessage();
const dialog = useDialog();

interface Category {
  id: number;
  name: string;
  parentId: number | null;
  level: number;
  icon?: string;
  color: string;
  description: string;
  sort: number;
  wallpaperCount: number;
  status: 'active' | 'inactive';
  createTime: string;
  updateTime: string;
  children?: Category[];
}

// 分类列表（树形结构）
const categoryList = ref<Category[]>([
  {
    id: 1,
    name: '风景',
    parentId: null,
    level: 1,
    icon: 'mdi:image-filter-hdr',
    color: '#4ade80',
    description: '自然风景、城市风光等',
    sort: 1,
    wallpaperCount: 2450,
    status: 'active',
    createTime: '2024-01-01',
    updateTime: '2024-01-15',
    children: [
      {
        id: 11,
        name: '自然风景',
        parentId: 1,
        level: 2,
        color: '#22c55e',
        description: '山川湖海、森林草原',
        sort: 1,
        wallpaperCount: 1200,
        status: 'active',
        createTime: '2024-01-01',
        updateTime: '2024-01-10'
      },
      {
        id: 12,
        name: '城市风光',
        parentId: 1,
        level: 2,
        color: '#14b8a6',
        description: '都市建筑、街道夜景',
        sort: 2,
        wallpaperCount: 850,
        status: 'active',
        createTime: '2024-01-01',
        updateTime: '2024-01-08'
      },
      {
        id: 13,
        name: '日落黄昏',
        parentId: 1,
        level: 2,
        color: '#fb923c',
        description: '日出日落、晚霞夕阳',
        sort: 3,
        wallpaperCount: 400,
        status: 'active',
        createTime: '2024-01-02',
        updateTime: '2024-01-09'
      }
    ]
  },
  {
    id: 2,
    name: '动漫',
    parentId: null,
    level: 1,
    icon: 'mdi:draw',
    color: '#f472b6',
    description: '动漫角色、场景等',
    sort: 2,
    wallpaperCount: 3680,
    status: 'active',
    createTime: '2024-01-01',
    updateTime: '2024-01-14',
    children: [
      {
        id: 21,
        name: '动漫角色',
        parentId: 2,
        level: 2,
        color: '#ec4899',
        description: '各类动漫人物角色',
        sort: 1,
        wallpaperCount: 2100,
        status: 'active',
        createTime: '2024-01-01',
        updateTime: '2024-01-12'
      },
      {
        id: 22,
        name: '动漫场景',
        parentId: 2,
        level: 2,
        color: '#db2777',
        description: '动漫世界场景背景',
        sort: 2,
        wallpaperCount: 1580,
        status: 'active',
        createTime: '2024-01-01',
        updateTime: '2024-01-11'
      }
    ]
  },
  {
    id: 3,
    name: '游戏',
    parentId: null,
    level: 1,
    icon: 'mdi:gamepad-variant',
    color: '#a78bfa',
    description: '游戏角色、场景、CG等',
    sort: 3,
    wallpaperCount: 2890,
    status: 'active',
    createTime: '2024-01-02',
    updateTime: '2024-01-13',
    children: [
      {
        id: 31,
        name: '游戏角色',
        parentId: 3,
        level: 2,
        color: '#8b5cf6',
        description: '游戏人物角色原画',
        sort: 1,
        wallpaperCount: 1650,
        status: 'active',
        createTime: '2024-01-02',
        updateTime: '2024-01-10'
      },
      {
        id: 32,
        name: '游戏CG',
        parentId: 3,
        level: 2,
        color: '#7c3aed',
        description: '游戏宣传CG、截图',
        sort: 2,
        wallpaperCount: 1240,
        status: 'active',
        createTime: '2024-01-02',
        updateTime: '2024-01-09'
      }
    ]
  },
  {
    id: 4,
    name: '抽象',
    parentId: null,
    level: 1,
    icon: 'mdi:blur',
    color: '#64748b',
    description: '抽象艺术、几何图形等',
    sort: 4,
    wallpaperCount: 980,
    status: 'active',
    createTime: '2024-01-03',
    updateTime: '2024-01-15'
  },
  {
    id: 5,
    name: '简约',
    parentId: null,
    level: 1,
    icon: 'mdi:square-outline',
    color: '#94a3b8',
    description: '极简主义、纯色背景等',
    sort: 5,
    wallpaperCount: 560,
    status: 'inactive',
    createTime: '2024-01-04',
    updateTime: '2024-01-14'
  }
]);

// 编辑模态框
const editModalVisible = ref(false);
const editForm = ref<Partial<Category>>({
  name: '',
  parentId: null,
  icon: '',
  color: '#4ade80',
  description: '',
  sort: 999,
  status: 'active'
});
const isEdit = ref(false);

// 展开的节点
const expandedKeys = ref<number[]>([1, 2, 3]);

// 将分类列表转换为树形结构选项
const treeOptions = computed((): TreeOption[] => {
  const convertToTreeOptions = (categories: Category[]): TreeOption[] => {
    return categories.map(cat => ({
      key: cat.id,
      label: cat.name,
      children: cat.children ? convertToTreeOptions(cat.children) : undefined
    }));
  };
  return convertToTreeOptions(categoryList.value);
});

// 获取所有父分类选项
const parentOptions = computed(() => {
  const options = [{ label: '无（作为一级分类）', value: null }];
  const addOptions = (categories: Category[], prefix = '') => {
    categories.forEach(cat => {
      if (cat.level < 2) { // 最多支持两级
        options.push({ 
          label: prefix + cat.name, 
          value: cat.id,
          disabled: editForm.value.id === cat.id // 不能选择自己作为父级
        });
        if (cat.children && cat.level === 1) {
          addOptions(cat.children, '　├─ ');
        }
      }
    });
  };
  addOptions(categoryList.value);
  return options;
});

// 平铺的分类列表（用于表格展示）
const flattenCategories = computed(() => {
  const result: Category[] = [];
  const flatten = (categories: Category[]) => {
    categories.forEach(cat => {
      result.push(cat);
      if (cat.children) {
        flatten(cat.children);
      }
    });
  };
  flatten(categoryList.value);
  return result;
});

// 统计数据
const statistics = computed(() => {
  const total = flattenCategories.value.length;
  const active = flattenCategories.value.filter(c => c.status === 'active').length;
  const totalWallpapers = flattenCategories.value.reduce((sum, c) => sum + c.wallpaperCount, 0);
  const level1 = categoryList.value.length;
  const level2 = flattenCategories.value.filter(c => c.level === 2).length;
  return { total, active, inactive: total - active, totalWallpapers, level1, level2 };
});

// 表格列定义
const columns: DataTableColumns<Category> = [
  {
    title: '分类名称',
    key: 'name',
    width: 200,
    render(row) {
      const prefix = row.level === 2 ? '　├─ ' : '';
      return h('div', { class: 'flex items-center gap-8px' }, [
        row.icon && h(NIcon, { size: 18, color: row.color }, {
          default: () => h(SvgIconVNode({ icon: row.icon! }))
        }),
        h('span', { style: { color: row.color } }, prefix + row.name)
      ]);
    }
  },
  {
    title: '描述',
    key: 'description',
    width: 250,
    ellipsis: {
      tooltip: true
    }
  },
  {
    title: '壁纸数量',
    key: 'wallpaperCount',
    width: 100,
    align: 'center',
    render(row) {
      return h(NTag, { type: 'info', size: 'small' }, {
        default: () => row.wallpaperCount.toLocaleString()
      });
    }
  },
  {
    title: '排序',
    key: 'sort',
    width: 80,
    align: 'center'
  },
  {
    title: '状态',
    key: 'status',
    width: 100,
    align: 'center',
    render(row) {
      return h(NSwitch, {
        value: row.status === 'active',
        onUpdateValue: (value: boolean) => handleToggleStatus(row, value)
      });
    }
  },
  {
    title: '更新时间',
    key: 'updateTime',
    width: 120
  },
  {
    title: '操作',
    key: 'action',
    width: 150,
    fixed: 'right',
    render(row) {
      return h(NSpace, {}, {
        default: () => [
          h(NButton, {
            size: 'small',
            onClick: () => handleEdit(row)
          }, { default: () => '编辑' }),
          h(NButton, {
            size: 'small',
            type: 'error',
            onClick: () => handleDelete(row),
            disabled: row.wallpaperCount > 0
          }, { default: () => '删除' })
        ]
      });
    }
  }
];

// 新增分类
const handleAdd = () => {
  isEdit.value = false;
  editForm.value = {
    name: '',
    parentId: null,
    icon: '',
    color: '#4ade80',
    description: '',
    sort: Math.max(...flattenCategories.value.map(c => c.sort), 0) + 1,
    status: 'active'
  };
  editModalVisible.value = true;
};

// 编辑分类
const handleEdit = (category: Category) => {
  isEdit.value = true;
  editForm.value = {
    id: category.id,
    name: category.name,
    parentId: category.parentId,
    icon: category.icon,
    color: category.color,
    description: category.description,
    sort: category.sort,
    status: category.status
  };
  editModalVisible.value = true;
};

// 删除分类
const handleDelete = (category: Category) => {
  if (category.wallpaperCount > 0) {
    message.warning('该分类下还有壁纸，无法删除');
    return;
  }
  
  dialog.warning({
    title: '确认删除',
    content: `确定要删除分类「${category.name}」吗？`,
    positiveText: '删除',
    negativeText: '取消',
    onPositiveClick: () => {
      // 从父级的children中删除或从根列表删除
      const removeFromList = (list: Category[]) => {
        const index = list.findIndex(c => c.id === category.id);
        if (index > -1) {
          list.splice(index, 1);
          return true;
        }
        for (const item of list) {
          if (item.children && removeFromList(item.children)) {
            if (item.children.length === 0) {
              delete item.children;
            }
            return true;
          }
        }
        return false;
      };
      
      if (removeFromList(categoryList.value)) {
        message.success('删除成功');
      }
    }
  });
};

// 切换状态
const handleToggleStatus = (category: Category, value: boolean) => {
  category.status = value ? 'active' : 'inactive';
  message.success(`已${value ? '启用' : '禁用'}`);
};

// 保存
const handleSave = () => {
  if (!editForm.value.name) {
    message.error('请输入分类名称');
    return;
  }
  
  if (isEdit.value) {
    // 编辑
    const updateInList = (list: Category[]): boolean => {
      for (const item of list) {
        if (item.id === editForm.value.id) {
          Object.assign(item, {
            ...editForm.value,
            updateTime: new Date().toISOString().split('T')[0]
          });
          return true;
        }
        if (item.children && updateInList(item.children)) {
          return true;
        }
      }
      return false;
    };
    
    if (updateInList(categoryList.value)) {
      message.success('更新成功');
    }
  } else {
    // 新增
    const newCategory: Category = {
      id: Math.max(...flattenCategories.value.map(c => c.id), 0) + 1,
      name: editForm.value.name!,
      parentId: editForm.value.parentId || null,
      level: editForm.value.parentId ? 2 : 1,
      icon: editForm.value.icon,
      color: editForm.value.color!,
      description: editForm.value.description || '',
      sort: editForm.value.sort || 999,
      wallpaperCount: 0,
      status: editForm.value.status || 'active',
      createTime: new Date().toISOString().split('T')[0],
      updateTime: new Date().toISOString().split('T')[0]
    };
    
    if (newCategory.parentId) {
      // 添加到父分类的children中
      const addToParent = (list: Category[]): boolean => {
        for (const item of list) {
          if (item.id === newCategory.parentId) {
            if (!item.children) {
              item.children = [];
            }
            item.children.push(newCategory);
            item.children.sort((a, b) => a.sort - b.sort);
            return true;
          }
          if (item.children && addToParent(item.children)) {
            return true;
          }
        }
        return false;
      };
      addToParent(categoryList.value);
    } else {
      // 添加到根列表
      categoryList.value.push(newCategory);
      categoryList.value.sort((a, b) => a.sort - b.sort);
    }
    
    message.success('添加成功');
  }
  
  editModalVisible.value = false;
};

// 刷新
const handleRefresh = () => {
  message.success('数据已刷新');
};

// 图标选项
const iconOptions = [
  { label: '风景', value: 'mdi:image-filter-hdr' },
  { label: '动漫', value: 'mdi:draw' },
  { label: '游戏', value: 'mdi:gamepad-variant' },
  { label: '抽象', value: 'mdi:blur' },
  { label: '简约', value: 'mdi:square-outline' },
  { label: '人物', value: 'mdi:account' },
  { label: '动物', value: 'mdi:paw' },
  { label: '科技', value: 'mdi:chip' },
  { label: '艺术', value: 'mdi:palette' },
  { label: '自定义', value: '' }
];
</script>

<template>
  <NSpace vertical :size="16">
    <!-- 统计卡片 -->
    <NCard :bordered="false">
      <div class="grid grid-cols-6 gap-16px">
        <div class="text-center">
          <div class="text-24px font-bold">{{ statistics.total }}</div>
          <div class="text-gray-500 mt-4px">分类总数</div>
        </div>
        <div class="text-center">
          <div class="text-24px font-bold text-blue-500">{{ statistics.level1 }}</div>
          <div class="text-gray-500 mt-4px">一级分类</div>
        </div>
        <div class="text-center">
          <div class="text-24px font-bold text-purple-500">{{ statistics.level2 }}</div>
          <div class="text-gray-500 mt-4px">二级分类</div>
        </div>
        <div class="text-center">
          <div class="text-24px font-bold text-green-500">{{ statistics.active }}</div>
          <div class="text-gray-500 mt-4px">已启用</div>
        </div>
        <div class="text-center">
          <div class="text-24px font-bold text-gray-500">{{ statistics.inactive }}</div>
          <div class="text-gray-500 mt-4px">已禁用</div>
        </div>
        <div class="text-center">
          <div class="text-24px font-bold text-orange-500">{{ statistics.totalWallpapers.toLocaleString() }}</div>
          <div class="text-gray-500 mt-4px">壁纸总数</div>
        </div>
      </div>
    </NCard>
    
    <!-- 操作按钮 -->
    <NCard :bordered="false">
      <NSpace>
        <NButton type="primary" @click="handleAdd">
          <template #icon>
            <component :is="SvgIconVNode({ icon: 'material-symbols:add' })" />
          </template>
          新增分类
        </NButton>
        <NButton @click="handleRefresh">
          <template #icon>
            <component :is="SvgIconVNode({ icon: 'material-symbols:refresh' })" />
          </template>
          刷新
        </NButton>
      </NSpace>
    </NCard>
    
    <!-- 分类列表 -->
    <NCard :bordered="false" title="分类列表">
      <NDataTable
        :columns="columns"
        :data="flattenCategories"
        :pagination="false"
        :bordered="false"
        :single-line="false"
        striped
      />
    </NCard>
    
    <!-- 编辑模态框 -->
    <NModal
      v-model:show="editModalVisible"
      :title="isEdit ? '编辑分类' : '新增分类'"
      preset="card"
      style="width: 600px"
    >
      <NForm :model="editForm" label-placement="left" label-width="100px">
        <NFormItem label="分类名称" required>
          <NInput v-model:value="editForm.name" placeholder="请输入分类名称" />
        </NFormItem>
        
        <NFormItem label="父级分类">
          <NSelect
            v-model:value="editForm.parentId"
            :options="parentOptions"
            placeholder="选择父级分类"
          />
        </NFormItem>
        
        <NFormItem label="图标">
          <NSpace>
            <NSelect
              v-model:value="editForm.icon"
              :options="iconOptions"
              placeholder="选择图标"
              style="width: 200px"
            />
            <NIcon v-if="editForm.icon" :size="24" :color="editForm.color">
              <component :is="SvgIconVNode({ icon: editForm.icon })" />
            </NIcon>
          </NSpace>
        </NFormItem>
        
        <NFormItem label="颜色">
          <NColorPicker v-model:value="editForm.color" :swatches="[
            '#4ade80', '#22c55e', '#14b8a6', '#06b6d4',
            '#3b82f6', '#6366f1', '#8b5cf6', '#a78bfa',
            '#ec4899', '#f472b6', '#f87171', '#fb923c',
            '#fbbf24', '#64748b', '#94a3b8'
          ]" />
        </NFormItem>
        
        <NFormItem label="描述">
          <NInput
            v-model:value="editForm.description"
            type="textarea"
            :rows="3"
            placeholder="请输入分类描述"
          />
        </NFormItem>
        
        <NFormItem label="排序">
          <NInputNumber v-model:value="editForm.sort" :min="0" placeholder="数字越小越靠前" />
        </NFormItem>
        
        <NFormItem label="状态">
          <NSwitch v-model:value="editForm.status" checked-value="active" unchecked-value="inactive">
            <template #checked>启用</template>
            <template #unchecked>禁用</template>
          </NSwitch>
        </NFormItem>
      </NForm>
      
      <template #footer>
        <NSpace justify="end">
          <NButton @click="editModalVisible = false">取消</NButton>
          <NButton type="primary" @click="handleSave">保存</NButton>
        </NSpace>
      </template>
    </NModal>
  </NSpace>
</template>

<style scoped>
</style>
