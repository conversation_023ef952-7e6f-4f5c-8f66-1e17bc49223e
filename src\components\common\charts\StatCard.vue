<script setup lang="ts">
import { computed } from 'vue';
import { NCard, NStatistic, NTag, NNumberAnimation, NSpace } from 'naive-ui';
import SvgIcon from '@/components/custom/svg-icon.vue';

interface Props {
  title: string;
  value: number;
  prefix?: string;
  suffix?: string;
  precision?: number;
  showAnimation?: boolean;
  animationDuration?: number;
  trend?: 'up' | 'down' | 'stable';
  trendValue?: number;
  trendText?: string;
  icon?: string;
  iconColor?: string;
  size?: 'small' | 'medium' | 'large';
  loading?: boolean;
  bordered?: boolean;
  hoverable?: boolean;
  color?: string;
  backgroundColor?: string;
}

const props = withDefaults(defineProps<Props>(), {
  precision: 0,
  showAnimation: true,
  animationDuration: 2000,
  trend: 'stable',
  size: 'medium',
  loading: false,
  bordered: true,
  hoverable: false
});

const emit = defineEmits<{
  click: [];
}>();

// 计算趋势颜色
const trendColor = computed(() => {
  switch (props.trend) {
    case 'up':
      return 'success';
    case 'down':
      return 'error';
    default:
      return 'default';
  }
});

// 计算趋势图标
const trendIcon = computed(() => {
  switch (props.trend) {
    case 'up':
      return 'carbon:trending-up';
    case 'down':
      return 'carbon:trending-down';
    default:
      return 'carbon:trending-flat';
  }
});

// 计算卡片样式
const cardStyle = computed(() => {
  const style: Record<string, string> = {};
  
  if (props.backgroundColor) {
    style.backgroundColor = props.backgroundColor;
  }
  
  if (props.hoverable) {
    style.cursor = 'pointer';
  }
  
  return style;
});

// 计算统计数值样式
const statisticStyle = computed(() => {
  const style: Record<string, string> = {};
  
  if (props.color) {
    style.color = props.color;
  }
  
  return style;
});

// 处理点击事件
const handleClick = () => {
  if (props.hoverable) {
    emit('click');
  }
};

// 格式化趋势值
const formatTrendValue = computed(() => {
  if (props.trendValue === undefined) return '';
  
  const sign = props.trendValue > 0 ? '+' : '';
  return `${sign}${props.trendValue}%`;
});
</script>

<template>
  <NCard
    :bordered="bordered"
    :size="size"
    :style="cardStyle"
    :class="{ 'hover:shadow-lg transition-shadow duration-300': hoverable }"
    @click="handleClick"
  >
    <NSpace vertical :size="8">
      <!-- 标题和图标 -->
      <div class="flex items-center justify-between">
        <span 
          class="text-gray-600 dark:text-gray-400"
          :class="{
            'text-sm': size === 'small',
            'text-base': size === 'medium',
            'text-lg': size === 'large'
          }"
        >
          {{ title }}
        </span>
        <SvgIcon
          v-if="icon"
          :icon="icon"
          :class="{
            'text-20px': size === 'small',
            'text-24px': size === 'medium',
            'text-28px': size === 'large'
          }"
          :style="{ color: iconColor }"
        />
      </div>

      <!-- 数值 -->
      <div class="flex items-baseline justify-between">
        <div :style="statisticStyle">
          <NNumberAnimation
            v-if="showAnimation && !loading"
            :from="0"
            :to="value"
            :duration="animationDuration"
            :precision="precision"
            :prefix="prefix"
            :suffix="suffix"
            show-separator
            :class="{
              'text-xl font-bold': size === 'small',
              'text-2xl font-bold': size === 'medium',
              'text-3xl font-bold': size === 'large'
            }"
          />
          <span
            v-else
            :class="{
              'text-xl font-bold': size === 'small',
              'text-2xl font-bold': size === 'medium',
              'text-3xl font-bold': size === 'large'
            }"
          >
            {{ prefix }}{{ value.toLocaleString() }}{{ suffix }}
          </span>
        </div>

        <!-- 趋势标签 -->
        <NTag
          v-if="trend !== 'stable' && (trendValue !== undefined || trendText)"
          :type="trendColor"
          size="small"
          round
        >
          <template #icon>
            <SvgIcon :icon="trendIcon" />
          </template>
          {{ trendText || formatTrendValue }}
        </NTag>
      </div>

      <!-- 插槽内容 -->
      <div v-if="$slots.default" class="mt-2">
        <slot />
      </div>
    </NSpace>
  </NCard>
</template>

<style scoped>
.hover\:shadow-lg:hover {
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-300 {
  transition-duration: 300ms;
}
</style>
